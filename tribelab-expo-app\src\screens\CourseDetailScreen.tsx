import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import Icon from "react-native-vector-icons/Ionicons";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import {
  fetchCourseDetails,
  enrollInCourse,
  setCurrentCourse,
  setCurrentModule,
  setCurrentLesson,
} from "../store/slices/courseSlice";
import Toast from "react-native-toast-message";

interface Course {
  id: string;
  title: string;
  description: string;
  instructor: string;
  duration: string;
  level: string;
  price: number;
  thumbnail: string;
  enrolled: boolean;
  progress: number;
  modules: Module[];
  rating: number;
  studentsCount: number;
}

interface Module {
  id: string;
  title: string;
  description: string;
  duration: string;
  lessons: Lesson[];
  completed: boolean;
}

interface Lesson {
  id: string;
  title: string;
  duration: string;
  type: "video" | "text" | "quiz";
  completed: boolean;
}

const CourseDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  const { currentCourse, isLoading } = useAppSelector((state) => state.course);
  const { user } = useAppSelector((state) => state.auth);

  const { courseId } = route.params as { courseId: string };
  const [enrolling, setEnrolling] = useState(false);

  useEffect(() => {
    loadCourseDetails();
  }, [courseId]);

  const loadCourseDetails = async () => {
    try {
      await dispatch(fetchCourseDetails(courseId)).unwrap();
    } catch (error) {
      console.error("Failed to load course details:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load course details",
      });

      // Fallback to mock data
      const mockCourse: Course = {
        id: courseId,
        title: "Advanced React Native Development",
        description:
          "Master advanced concepts in React Native development including navigation, state management, animations, and performance optimization.",
        instructor: "Sarah Johnson",
        duration: "8 weeks",
        level: "Advanced",
        price: 199,
        thumbnail: "https://via.placeholder.com/400x200",
        enrolled: false,
        progress: 0,
        rating: 4.8,
        studentsCount: 1247,
        modules: [
          {
            id: "1",
            title: "Advanced Navigation",
            description: "Deep dive into React Navigation",
            duration: "2 hours",
            completed: false,
            lessons: [
              {
                id: "1",
                title: "Stack Navigation",
                duration: "30 min",
                type: "video",
                completed: false,
              },
              {
                id: "2",
                title: "Tab Navigation",
                duration: "25 min",
                type: "video",
                completed: false,
              },
              {
                id: "3",
                title: "Navigation Quiz",
                duration: "10 min",
                type: "quiz",
                completed: false,
              },
            ],
          },
          {
            id: "2",
            title: "State Management",
            description: "Redux, Context API, and Zustand",
            duration: "3 hours",
            completed: false,
            lessons: [
              {
                id: "4",
                title: "Redux Fundamentals",
                duration: "45 min",
                type: "video",
                completed: false,
              },
              {
                id: "5",
                title: "Context API",
                duration: "30 min",
                type: "video",
                completed: false,
              },
              {
                id: "6",
                title: "Zustand Implementation",
                duration: "40 min",
                type: "video",
                completed: false,
              },
            ],
          },
        ],
      };

      setCourse(mockCourse);
    }
  };

  const handleEnroll = async () => {
    if (!currentCourse) return;

    try {
      setEnrolling(true);
      await dispatch(enrollInCourse(currentCourse.id)).unwrap();

      Toast.show({
        type: "success",
        text1: "Enrolled Successfully!",
        text2: "You can now access all course content",
      });
    } catch (error: any) {
      console.error("Failed to enroll:", error);
      Toast.show({
        type: "error",
        text1: "Enrollment Failed",
        text2: error || "Failed to enroll in course",
      });
    } finally {
      setEnrolling(false);
    }
  };

  const handleLessonPress = (lesson: Lesson) => {
    navigation.navigate(
      "LessonView" as never,
      { lessonId: lesson.id, courseId } as never
    );
  };

  if (isLoading && !currentCourse) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#000" />
          <Text style={styles.loadingText}>Loading course details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentCourse) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Course not found</Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Course Details</Text>
        <TouchableOpacity>
          <Icon name="share-outline" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Image
          source={{
            uri:
              currentCourse.thumbnail || "https://via.placeholder.com/400x200",
          }}
          style={styles.thumbnail}
        />

        <View style={styles.courseInfo}>
          <Text style={styles.courseTitle}>{currentCourse.title}</Text>
          <Text style={styles.instructor}>
            by {currentCourse.instructor?.name || "Unknown Instructor"}
          </Text>

          <View style={styles.statsRow}>
            <View style={styles.stat}>
              <Icon name="star" size={16} color="#FFD700" />
              <Text style={styles.statText}>{course.rating}</Text>
            </View>
            <View style={styles.stat}>
              <Icon name="people" size={16} color="#666" />
              <Text style={styles.statText}>
                {course.studentsCount} students
              </Text>
            </View>
            <View style={styles.stat}>
              <Icon name="time" size={16} color="#666" />
              <Text style={styles.statText}>{course.duration}</Text>
            </View>
            <View style={styles.stat}>
              <Icon name="bar-chart" size={16} color="#666" />
              <Text style={styles.statText}>{course.level}</Text>
            </View>
          </View>

          <Text style={styles.description}>{course.description}</Text>

          {course.enrolled ? (
            <View style={styles.progressSection}>
              <Text style={styles.progressTitle}>Your Progress</Text>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${course.progress}%` },
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {course.progress}% Complete
              </Text>
            </View>
          ) : (
            <View style={styles.priceSection}>
              <Text style={styles.price}>${course.price}</Text>
              <TouchableOpacity
                style={[
                  styles.enrollButton,
                  enrolling && styles.enrollButtonDisabled,
                ]}
                onPress={handleEnroll}
                disabled={enrolling}
              >
                {enrolling ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.enrollButtonText}>Enroll Now</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.modulesSection}>
          <Text style={styles.sectionTitle}>Course Modules</Text>
          {course.modules.map((module, index) => (
            <View key={module.id} style={styles.moduleCard}>
              <View style={styles.moduleHeader}>
                <Text style={styles.moduleTitle}>
                  {index + 1}. {module.title}
                </Text>
                <Text style={styles.moduleDuration}>{module.duration}</Text>
              </View>
              <Text style={styles.moduleDescription}>{module.description}</Text>

              <View style={styles.lessonsContainer}>
                {module.lessons.map((lesson) => (
                  <TouchableOpacity
                    key={lesson.id}
                    style={styles.lessonItem}
                    onPress={() => handleLessonPress(lesson)}
                    disabled={!course.enrolled}
                  >
                    <Icon
                      name={
                        lesson.type === "video"
                          ? "play-circle"
                          : lesson.type === "quiz"
                            ? "help-circle"
                            : "document-text"
                      }
                      size={20}
                      color={course.enrolled ? "#000" : "#ccc"}
                    />
                    <View style={styles.lessonInfo}>
                      <Text
                        style={[
                          styles.lessonTitle,
                          !course.enrolled && styles.disabledText,
                        ]}
                      >
                        {lesson.title}
                      </Text>
                      <Text
                        style={[
                          styles.lessonDuration,
                          !course.enrolled && styles.disabledText,
                        ]}
                      >
                        {lesson.duration}
                      </Text>
                    </View>
                    {lesson.completed && (
                      <Icon name="checkmark-circle" size={20} color="#4CAF50" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
    marginBottom: 20,
  },
  backButton: {
    backgroundColor: "#000",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontWeight: "600",
  },
  thumbnail: {
    width: "100%",
    height: 200,
    resizeMode: "cover",
  },
  courseInfo: {
    padding: 20,
  },
  courseTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 5,
  },
  instructor: {
    fontSize: 16,
    color: "#666",
    marginBottom: 15,
  },
  statsRow: {
    flexDirection: "row",
    marginBottom: 15,
  },
  stat: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 15,
  },
  statText: {
    marginLeft: 5,
    fontSize: 14,
    color: "#666",
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
    marginBottom: 20,
  },
  progressSection: {
    marginBottom: 20,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: "#f0f0f0",
    borderRadius: 4,
    marginBottom: 5,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#4CAF50",
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: "#666",
  },
  priceSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  price: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000",
  },
  enrollButton: {
    backgroundColor: "#000",
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 8,
  },
  enrollButtonDisabled: {
    opacity: 0.6,
  },
  enrollButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  modulesSection: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 15,
  },
  moduleCard: {
    backgroundColor: "#f9f9f9",
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  moduleHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 5,
  },
  moduleTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  moduleDuration: {
    fontSize: 14,
    color: "#666",
  },
  moduleDescription: {
    fontSize: 14,
    color: "#666",
    marginBottom: 10,
  },
  lessonsContainer: {
    marginTop: 10,
  },
  lessonItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  lessonInfo: {
    flex: 1,
    marginLeft: 10,
  },
  lessonTitle: {
    fontSize: 14,
    fontWeight: "500",
  },
  lessonDuration: {
    fontSize: 12,
    color: "#666",
  },
  disabledText: {
    color: "#ccc",
  },
});

export default CourseDetailScreen;
