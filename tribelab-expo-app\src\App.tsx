import "react-native-gesture-handler";
import React, { useEffect } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import Toast from "react-native-toast-message";
import ReduxProvider from "./providers/ReduxProvider";
import NotificationProvider from "./providers/NotificationProvider";
import AppNavigator from "./navigation/AppNavigator";
import notificationService from "./services/notifications";

const App = () => {
  useEffect(() => {
    // Initialize notification service
    notificationService.initialize();
  }, []);

  return (
    <ReduxProvider>
      <SafeAreaProvider>
        <NotificationProvider>
          <StatusBar style="auto" />
          <AppNavigator />
          <Toast />
        </NotificationProvider>
      </SafeAreaProvider>
    </ReduxProvider>
  );
};

export default App;
