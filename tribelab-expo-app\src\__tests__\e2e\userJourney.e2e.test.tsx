import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { render, mockDataGenerators, testHelpers } from '../utils/testUtils';
import App from '../../App';
import authService from '../../services/auth';
import communityService from '../../services/community';
import courseService from '../../services/course';

// Mock all services
jest.mock('../../services/auth');
jest.mock('../../services/community');
jest.mock('../../services/course');
jest.mock('../../services/payment');
jest.mock('../../services/chat');

const mockAuthService = authService as jest.Mocked<typeof authService>;
const mockCommunityService = communityService as jest.Mocked<typeof communityService>;
const mockCourseService = courseService as jest.Mocked<typeof courseService>;

describe('End-to-End User Journey Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset all mocks to default successful responses
    setupDefaultMocks();
  });

  const setupDefaultMocks = () => {
    const mockUser = mockDataGenerators.user();
    const mockCommunities = testHelpers.generateArray(() => mockDataGenerators.community(), 5);
    const mockCourses = testHelpers.generateArray(() => mockDataGenerators.course(), 3);

    mockAuthService.getCurrentUser.mockResolvedValue(mockUser);
    mockAuthService.isAuthenticated.mockResolvedValue(true);
    mockCommunityService.getCommunities.mockResolvedValue({
      communities: mockCommunities,
      pagination: { page: 1, hasMore: false },
    });
    mockCourseService.getCourses.mockResolvedValue({
      courses: mockCourses,
      pagination: { page: 1, hasMore: false },
    });
  };

  describe('New User Onboarding Journey', () => {
    it('completes full registration and onboarding flow', async () => {
      const mockUser = mockDataGenerators.user();
      
      // Mock registration success
      mockAuthService.register.mockResolvedValue({
        user: mockUser,
        token: 'new-user-token',
      });

      const { getByTestId, getByText } = render(<App />);

      // Step 1: Navigate to registration
      await waitFor(() => {
        const getStartedButton = getByText('Get Started');
        fireEvent.press(getStartedButton);
      });

      // Step 2: Fill registration form
      const nameInput = getByTestId('name-input');
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const confirmPasswordInput = getByTestId('confirm-password-input');

      fireEvent.changeText(nameInput, 'New User');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'securepassword123');
      fireEvent.changeText(confirmPasswordInput, 'securepassword123');

      const registerButton = getByText('Create Account');
      fireEvent.press(registerButton);

      // Step 3: Verify registration success
      await waitFor(() => {
        expect(mockAuthService.register).toHaveBeenCalledWith({
          name: 'New User',
          email: '<EMAIL>',
          password: 'securepassword123',
        });
      });

      // Step 4: Complete onboarding
      await waitFor(() => {
        expect(getByText('Welcome to TribeLab!')).toBeTruthy();
      });

      // Step 5: Select interests
      const interestButtons = ['Technology', 'Design', 'Business'];
      interestButtons.forEach(interest => {
        const button = getByText(interest);
        fireEvent.press(button);
      });

      const continueButton = getByText('Continue');
      fireEvent.press(continueButton);

      // Step 6: Verify navigation to main app
      await waitFor(() => {
        expect(getByText('Discover')).toBeTruthy(); // Main tab navigation
      });
    });

    it('handles registration errors gracefully', async () => {
      mockAuthService.register.mockRejectedValue(new Error('Email already exists'));

      const { getByTestId, getByText } = render(<App />);

      // Navigate to registration and fill form
      const getStartedButton = getByText('Get Started');
      fireEvent.press(getStartedButton);

      const nameInput = getByTestId('name-input');
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const confirmPasswordInput = getByTestId('confirm-password-input');

      fireEvent.changeText(nameInput, 'Test User');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'password123');

      const registerButton = getByText('Create Account');
      fireEvent.press(registerButton);

      // Verify error handling
      await waitFor(() => {
        expect(getByText('Email already exists')).toBeTruthy();
      });

      // User should still be on registration screen
      expect(getByText('Create Account')).toBeTruthy();
    });
  });

  describe('Community Discovery and Joining Journey', () => {
    it('discovers and joins a community successfully', async () => {
      const mockCommunity = mockDataGenerators.community({
        name: 'React Native Developers',
        memberCount: 1500,
      });

      mockCommunityService.joinCommunity.mockResolvedValue({
        success: true,
        community: { ...mockCommunity, isMember: true },
      });

      const { getByText, getByTestId } = render(<App />);

      // Step 1: Navigate to communities
      await waitFor(() => {
        const communitiesTab = getByText('Communities');
        fireEvent.press(communitiesTab);
      });

      // Step 2: Browse communities
      await waitFor(() => {
        expect(getByText('React Native Developers')).toBeTruthy();
      });

      // Step 3: View community details
      const communityCard = getByTestId(`community-card-${mockCommunity.id}`);
      fireEvent.press(communityCard);

      await waitFor(() => {
        expect(getByText('1,500 members')).toBeTruthy();
      });

      // Step 4: Join community
      const joinButton = getByText('Join Community');
      fireEvent.press(joinButton);

      await waitFor(() => {
        expect(mockCommunityService.joinCommunity).toHaveBeenCalledWith(mockCommunity.id);
      });

      // Step 5: Verify join success
      await waitFor(() => {
        expect(getByText('Joined')).toBeTruthy();
      });
    });

    it('searches for specific communities', async () => {
      const searchResults = [
        mockDataGenerators.community({ name: 'JavaScript Developers' }),
        mockDataGenerators.community({ name: 'React Enthusiasts' }),
      ];

      mockCommunityService.searchCommunities.mockResolvedValue({
        communities: searchResults,
        pagination: { page: 1, hasMore: false },
      });

      const { getByTestId, getByText } = render(<App />);

      // Navigate to communities
      const communitiesTab = getByText('Communities');
      fireEvent.press(communitiesTab);

      // Use search
      const searchInput = getByTestId('community-search-input');
      fireEvent.changeText(searchInput, 'JavaScript');

      await waitFor(() => {
        expect(mockCommunityService.searchCommunities).toHaveBeenCalledWith('JavaScript');
      });

      // Verify search results
      await waitFor(() => {
        expect(getByText('JavaScript Developers')).toBeTruthy();
        expect(getByText('React Enthusiasts')).toBeTruthy();
      });
    });
  });

  describe('Course Purchase and Learning Journey', () => {
    it('completes course purchase and starts learning', async () => {
      const mockCourse = mockDataGenerators.course({
        title: 'Complete React Native Course',
        price: 99,
      });

      const mockPaymentMethod = mockDataGenerators.paymentMethod();

      mockCourseService.getCourseDetails.mockResolvedValue(mockCourse);
      mockCourseService.purchaseCourse.mockResolvedValue({
        success: true,
        transactionId: 'txn_123',
      });

      const { getByText, getByTestId } = render(<App />);

      // Step 1: Navigate to courses
      const coursesTab = getByText('Courses');
      fireEvent.press(coursesTab);

      // Step 2: Select a course
      await waitFor(() => {
        const courseCard = getByTestId(`course-card-${mockCourse.id}`);
        fireEvent.press(courseCard);
      });

      // Step 3: View course details
      await waitFor(() => {
        expect(getByText('Complete React Native Course')).toBeTruthy();
        expect(getByText('$99')).toBeTruthy();
      });

      // Step 4: Start purchase
      const enrollButton = getByText('Enroll Now');
      fireEvent.press(enrollButton);

      // Step 5: Complete payment
      await waitFor(() => {
        const paymentButton = getByText('Complete Purchase');
        fireEvent.press(paymentButton);
      });

      await waitFor(() => {
        expect(mockCourseService.purchaseCourse).toHaveBeenCalledWith(
          mockCourse.id,
          expect.any(Object)
        );
      });

      // Step 6: Start learning
      await waitFor(() => {
        expect(getByText('Start Learning')).toBeTruthy();
      });

      const startLearningButton = getByText('Start Learning');
      fireEvent.press(startLearningButton);

      // Step 7: Verify navigation to course content
      await waitFor(() => {
        expect(getByText('Lesson 1')).toBeTruthy();
      });
    });

    it('handles payment failures gracefully', async () => {
      const mockCourse = mockDataGenerators.course();

      mockCourseService.purchaseCourse.mockRejectedValue(
        new Error('Payment failed')
      );

      const { getByText, getByTestId } = render(<App />);

      // Navigate to course and attempt purchase
      const coursesTab = getByText('Courses');
      fireEvent.press(coursesTab);

      const courseCard = getByTestId(`course-card-${mockCourse.id}`);
      fireEvent.press(courseCard);

      const enrollButton = getByText('Enroll Now');
      fireEvent.press(enrollButton);

      const paymentButton = getByText('Complete Purchase');
      fireEvent.press(paymentButton);

      // Verify error handling
      await waitFor(() => {
        expect(getByText('Payment failed')).toBeTruthy();
      });

      // User should be able to retry
      expect(getByText('Try Again')).toBeTruthy();
    });
  });

  describe('Social Features Journey', () => {
    it('creates post and receives engagement', async () => {
      const mockPost = mockDataGenerators.post({
        content: 'Just completed my first React Native app!',
      });

      mockCommunityService.createPost.mockResolvedValue(mockPost);
      mockCommunityService.likePost.mockResolvedValue({ success: true });

      const { getByText, getByTestId } = render(<App />);

      // Step 1: Navigate to a community
      const communitiesTab = getByText('Communities');
      fireEvent.press(communitiesTab);

      const communityCard = getByTestId('community-card-community1');
      fireEvent.press(communityCard);

      // Step 2: Create a post
      const createPostButton = getByText('Create Post');
      fireEvent.press(createPostButton);

      const postInput = getByTestId('post-content-input');
      fireEvent.changeText(postInput, 'Just completed my first React Native app!');

      const publishButton = getByText('Publish');
      fireEvent.press(publishButton);

      await waitFor(() => {
        expect(mockCommunityService.createPost).toHaveBeenCalled();
      });

      // Step 3: Verify post appears in feed
      await waitFor(() => {
        expect(getByText('Just completed my first React Native app!')).toBeTruthy();
      });

      // Step 4: Simulate receiving a like
      const likeButton = getByTestId(`like-button-${mockPost.id}`);
      fireEvent.press(likeButton);

      await waitFor(() => {
        expect(mockCommunityService.likePost).toHaveBeenCalledWith(mockPost.id);
      });
    });
  });

  describe('Chat and Messaging Journey', () => {
    it('starts conversation and sends messages', async () => {
      const mockUser = mockDataGenerators.user({ name: 'John Doe' });
      const mockChat = {
        id: 'chat-123',
        participants: [mockUser],
        messages: [],
      };

      mockCommunityService.startDirectMessage.mockResolvedValue(mockChat);

      const { getByText, getByTestId } = render(<App />);

      // Step 1: Navigate to user profile
      const profileButton = getByTestId('user-profile-button');
      fireEvent.press(profileButton);

      // Step 2: Start direct message
      const messageButton = getByText('Send Message');
      fireEvent.press(messageButton);

      await waitFor(() => {
        expect(mockCommunityService.startDirectMessage).toHaveBeenCalledWith(mockUser.id);
      });

      // Step 3: Send a message
      const messageInput = getByTestId('message-input');
      fireEvent.changeText(messageInput, 'Hello! How are you?');

      const sendButton = getByTestId('send-button');
      fireEvent.press(sendButton);

      // Step 4: Verify message appears
      await waitFor(() => {
        expect(getByText('Hello! How are you?')).toBeTruthy();
      });
    });
  });

  describe('Settings and Profile Management Journey', () => {
    it('updates profile information successfully', async () => {
      const updatedUser = mockDataGenerators.user({
        name: 'Updated Name',
        bio: 'Updated bio',
      });

      mockAuthService.updateProfile.mockResolvedValue(updatedUser);

      const { getByText, getByTestId } = render(<App />);

      // Step 1: Navigate to profile
      const profileTab = getByText('Profile');
      fireEvent.press(profileTab);

      // Step 2: Edit profile
      const editButton = getByText('Edit Profile');
      fireEvent.press(editButton);

      // Step 3: Update information
      const nameInput = getByTestId('profile-name-input');
      const bioInput = getByTestId('profile-bio-input');

      fireEvent.changeText(nameInput, 'Updated Name');
      fireEvent.changeText(bioInput, 'Updated bio');

      const saveButton = getByText('Save Changes');
      fireEvent.press(saveButton);

      await waitFor(() => {
        expect(mockAuthService.updateProfile).toHaveBeenCalledWith({
          name: 'Updated Name',
          bio: 'Updated bio',
        });
      });

      // Step 4: Verify updates
      await waitFor(() => {
        expect(getByText('Profile updated successfully')).toBeTruthy();
      });
    });
  });
});
