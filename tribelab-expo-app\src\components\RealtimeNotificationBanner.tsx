import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  PanGestureHandler,
  State,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { markNotificationAsRead } from '../store/slices/notificationSlice';

interface NotificationBannerProps {
  notification: {
    id: string;
    type: string;
    title: string;
    message: string;
    data?: any;
    user?: {
      id: string;
      name: string;
      avatar?: string;
    };
  };
  onDismiss: () => void;
  duration?: number;
}

const { width: screenWidth } = Dimensions.get('window');

const RealtimeNotificationBanner: React.FC<NotificationBannerProps> = ({
  notification,
  onDismiss,
  duration = 4000,
}) => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  
  const [slideAnim] = useState(new Animated.Value(-100));
  const [panAnim] = useState(new Animated.Value(0));
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Slide in animation
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    // Auto dismiss after duration
    const timer = setTimeout(() => {
      handleDismiss();
    }, duration);

    return () => clearTimeout(timer);
  }, []);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(panAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsVisible(false);
      onDismiss();
    });
  };

  const handleSwipeGesture = (event: any) => {
    const { translationY, state } = event.nativeEvent;

    if (state === State.ACTIVE) {
      if (translationY < 0) {
        panAnim.setValue(translationY);
      }
    } else if (state === State.END) {
      if (translationY < -50) {
        // Swipe up to dismiss
        handleDismiss();
      } else {
        // Snap back
        Animated.spring(panAnim, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  const handlePress = () => {
    // Mark as read
    dispatch(markNotificationAsRead(notification.id));
    
    // Navigate based on notification type
    handleNotificationNavigation();
    
    // Dismiss banner
    handleDismiss();
  };

  const handleNotificationNavigation = () => {
    const { type, data } = notification;

    try {
      switch (type) {
        case 'message':
          if (data?.chatId) {
            navigation.navigate('Chat', { chatId: data.chatId });
          } else if (data?.senderId) {
            navigation.navigate('Chat', { userId: data.senderId });
          }
          break;
          
        case 'community':
          if (data?.communityId) {
            navigation.navigate('Community', { communityId: data.communityId });
          }
          break;
          
        case 'course':
          if (data?.courseId) {
            navigation.navigate('CourseDetail', { courseId: data.courseId });
          }
          break;
          
        case 'post_like':
        case 'post_comment':
          if (data?.postId) {
            navigation.navigate('PostDetail', { postId: data.postId });
          }
          break;
          
        case 'payment':
          navigation.navigate('PaymentHistory');
          break;
          
        default:
          navigation.navigate('NotificationsFeed');
          break;
      }
    } catch (error) {
      console.error('Navigation error:', error);
      navigation.navigate('NotificationsFeed');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message':
        return 'chatbubble';
      case 'community':
        return 'people';
      case 'course':
        return 'school';
      case 'post_like':
        return 'heart';
      case 'post_comment':
        return 'chatbubble-outline';
      case 'payment':
        return 'card';
      case 'system':
        return 'settings';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'message':
        return '#007AFF';
      case 'community':
        return '#34C759';
      case 'course':
        return '#FF9500';
      case 'post_like':
        return '#FF3B30';
      case 'post_comment':
        return '#007AFF';
      case 'payment':
        return '#5856D6';
      case 'system':
        return '#8E8E93';
      default:
        return '#007AFF';
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <PanGestureHandler onGestureEvent={handleSwipeGesture}>
      <Animated.View
        style={[
          styles.container,
          {
            transform: [
              { translateY: slideAnim },
              { translateY: panAnim },
            ],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.banner}
          onPress={handlePress}
          activeOpacity={0.9}
        >
          <View style={styles.content}>
            <View style={[
              styles.iconContainer,
              { backgroundColor: getNotificationColor(notification.type) }
            ]}>
              <Icon
                name={getNotificationIcon(notification.type)}
                size={20}
                color="#fff"
              />
            </View>

            <View style={styles.textContainer}>
              <Text style={styles.title} numberOfLines={1}>
                {notification.title}
              </Text>
              <Text style={styles.message} numberOfLines={2}>
                {notification.message}
              </Text>
              {notification.user && (
                <Text style={styles.sender} numberOfLines={1}>
                  from {notification.user.name}
                </Text>
              )}
            </View>

            <TouchableOpacity
              style={styles.dismissButton}
              onPress={handleDismiss}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Icon name="close" size={18} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Progress bar */}
          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  backgroundColor: getNotificationColor(notification.type),
                  width: `${(duration - 1000) / duration * 100}%`,
                },
              ]}
            />
          </View>
        </TouchableOpacity>
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingHorizontal: 16,
    paddingTop: 50, // Account for status bar
  },
  banner: {
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 2,
  },
  sender: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  dismissButton: {
    padding: 4,
  },
  progressContainer: {
    height: 3,
    backgroundColor: '#f0f0f0',
  },
  progressBar: {
    height: '100%',
    borderRadius: 1.5,
  },
});

export default RealtimeNotificationBanner;
