# Changelog

## [Unreleased](https://github.com/razorpay/react-native-razorpay/tree/HEAD)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.1.33...HEAD)

**Closed issues:**

- image not found in react-native-customui  [\#270](https://github.com/razorpay/react-native-razorpay/issues/270)
- Razorpay - Facing the issue image not loaded [\#269](https://github.com/razorpay/react-native-razorpay/issues/269)
- App is getting crashed in android when i call open function. [\#265](https://github.com/razorpay/react-native-razorpay/issues/265)
- \[CRITICAL\] Payment amt value mapping varies in RazorPay UI [\#264](https://github.com/razorpay/react-native-razorpay/issues/264)
- React native app is crashing after confirming payment with razorpay test key? [\#261](https://github.com/razorpay/react-native-razorpay/issues/261)
- Unable to build iOS project, Xcode stucks while compiling react-native-razorpay module [\#258](https://github.com/razorpay/react-native-razorpay/issues/258)
- Use of undeclared identifier 'Razorpay' in RazorpayCheckout.m [\#256](https://github.com/razorpay/react-native-razorpay/issues/256)
- Country code issue on Razorpay checkout page [\#255](https://github.com/razorpay/react-native-razorpay/issues/255)
- Library not loaded [\#254](https://github.com/razorpay/react-native-razorpay/issues/254)
- Facing an issue pasted Below after doing Archive and failed to Distribute App. [\#253](https://github.com/razorpay/react-native-razorpay/issues/253)
- Undefined symbol: \_OBJC\_CLASS\_$\_GeneratedPluginRegistrant while building flutter app in ios [\#252](https://github.com/razorpay/react-native-razorpay/issues/252)
- ld:framework not found razorpay\_flutter  [\#251](https://github.com/razorpay/react-native-razorpay/issues/251)
- dyld: Library not loaded: @rpath/libswiftCore.dylib [\#250](https://github.com/razorpay/react-native-razorpay/issues/250)
- Getting success callback event for failed payment [\#249](https://github.com/razorpay/react-native-razorpay/issues/249)
- error: no such file or directory /libRazorpayCheckout.a [\#248](https://github.com/razorpay/react-native-razorpay/issues/248)
- How would I go about implementing custom checkout? [\#246](https://github.com/razorpay/react-native-razorpay/issues/246)
- React Native IOS not working - No podspec file was found [\#241](https://github.com/razorpay/react-native-razorpay/issues/241)
- Failed to verify bitcode: Linker option verification failed for bundle [\#238](https://github.com/razorpay/react-native-razorpay/issues/238)
- \[Need clarification\] Why is the discrepancy between module names and module import [\#236](https://github.com/razorpay/react-native-razorpay/issues/236)
- Getting a Blank Screen for a while before rendering razorpay content [\#235](https://github.com/razorpay/react-native-razorpay/issues/235)
- Payment Failed with following response  [\#233](https://github.com/razorpay/react-native-razorpay/issues/233)
- found an unexpected Mach-O header code: 0x72613c21 in Xcode 11 [\#232](https://github.com/razorpay/react-native-razorpay/issues/232)
- \[iOS\]: Could not find or use auto-linked framework 'Razorpay' [\#230](https://github.com/razorpay/react-native-razorpay/issues/230)
-  linker command failed with exit code 1 \(use -v to see invocation\) [\#228](https://github.com/razorpay/react-native-razorpay/issues/228)
- razor pay carshing app  in iOS react-native [\#224](https://github.com/razorpay/react-native-razorpay/issues/224)
- Always getting  dyld: Library not loaded: @rpath/libswiftCore.dylib [\#221](https://github.com/razorpay/react-native-razorpay/issues/221)
- Razor pay not working in react native \>=0.60 [\#219](https://github.com/razorpay/react-native-razorpay/issues/219)
- Min SDK version issue [\#214](https://github.com/razorpay/react-native-razorpay/issues/214)
- Error while Archiving Objective c app\(Found an unexpected Mach-O header code: 0x72613c21\) [\#212](https://github.com/razorpay/react-native-razorpay/issues/212)
- Do not find .podspec file [\#202](https://github.com/razorpay/react-native-razorpay/issues/202)
- app crashes in production in  app review  process [\#200](https://github.com/razorpay/react-native-razorpay/issues/200)
- iPhone / iPad version 10.3 crashed. [\#196](https://github.com/razorpay/react-native-razorpay/issues/196)
- Unable to link manually as well [\#193](https://github.com/razorpay/react-native-razorpay/issues/193)
- React Native RazorPay android Undefined|Undefiend showing when click pay Button [\#184](https://github.com/razorpay/react-native-razorpay/issues/184)
- Crash on iOS when adding a credit/debit card [\#180](https://github.com/razorpay/react-native-razorpay/issues/180)
- Facing the issue image not loaded [\#174](https://github.com/razorpay/react-native-razorpay/issues/174)
- Execution failed for task ':app:preDebugBuild' [\#158](https://github.com/razorpay/react-native-razorpay/issues/158)
- Submit to App Store issues: Unsupported Architecture x86 [\#60](https://github.com/razorpay/react-native-razorpay/issues/60)

**Merged pull requests:**

- Issue template changes added + Sample app updates. [\#262](https://github.com/razorpay/react-native-razorpay/pull/262) ([Nautiyalsachin](https://github.com/Nautiyalsachin))
- Swift5.2 update [\#257](https://github.com/razorpay/react-native-razorpay/pull/257) ([Nautiyalsachin](https://github.com/Nautiyalsachin))

## [v2.1.33](https://github.com/razorpay/react-native-razorpay/tree/v2.1.33) (2020-02-05)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.1.25...v2.1.33)

**Closed issues:**

- Undefined symbol: \_OBJC\_CLASS\_$\_\_TtC8Razorpay8Razorpay [\#243](https://github.com/razorpay/react-native-razorpay/issues/243)
- 'Razorpay/Razorpay-Swift.h' file not found [\#242](https://github.com/razorpay/react-native-razorpay/issues/242)
- Payment Amount Is Not Proper Show  [\#240](https://github.com/razorpay/react-native-razorpay/issues/240)
- RN 0.60+ autolinking support for IOS [\#239](https://github.com/razorpay/react-native-razorpay/issues/239)
- dyld: Library not loaded: @rpath/Razorpay.framework/Razorpay image not found [\#231](https://github.com/razorpay/react-native-razorpay/issues/231)
- Invariant Violation: Native module cannot be null. [\#227](https://github.com/razorpay/react-native-razorpay/issues/227)
- \(Android\) App crashing on release- Java ClassNotFoundException [\#225](https://github.com/razorpay/react-native-razorpay/issues/225)
- Razorpay.framework did not contain a "archived-expanded-entitlements.xcent" resource. [\#220](https://github.com/razorpay/react-native-razorpay/issues/220)
- react-native-razorpay not working in release apk [\#218](https://github.com/razorpay/react-native-razorpay/issues/218)
- code signing "razorpay.framework" failed. view distribution logs for more information. [\#213](https://github.com/razorpay/react-native-razorpay/issues/213)
- Unable to start activity ComponentInfo com.razorpay.CheckoutActivity : Didn't find class "com.razorpay.G\_G [\#210](https://github.com/razorpay/react-native-razorpay/issues/210)
- React native razorpay version for \> RN 60.0 - Auto link support [\#207](https://github.com/razorpay/react-native-razorpay/issues/207)
- Assemble not working with :react-native:0.20.+. [\#204](https://github.com/razorpay/react-native-razorpay/issues/204)
- Razorpay on ios not opening payment module [\#186](https://github.com/razorpay/react-native-razorpay/issues/186)

**Merged pull requests:**

- React 0.61  [\#245](https://github.com/razorpay/react-native-razorpay/pull/245) ([Nautiyalsachin](https://github.com/Nautiyalsachin))
- Updated installation process. [\#244](https://github.com/razorpay/react-native-razorpay/pull/244) ([Nautiyalsachin](https://github.com/Nautiyalsachin))
- Feat : Updated iOS framework. [\#226](https://github.com/razorpay/react-native-razorpay/pull/226) ([Nautiyalsachin](https://github.com/Nautiyalsachin))
- Declare react and react-native as peer dependencies [\#216](https://github.com/razorpay/react-native-razorpay/pull/216) ([ChintanAcharya](https://github.com/ChintanAcharya))
- \[Android\] Fix compilation error [\#209](https://github.com/razorpay/react-native-razorpay/pull/209) ([ChintanAcharya](https://github.com/ChintanAcharya))
- updated razorpay framework for iOS. [\#208](https://github.com/razorpay/react-native-razorpay/pull/208) ([Nautiyalsachin](https://github.com/Nautiyalsachin))
- Updated link to CONTRIBUTION.md document [\#206](https://github.com/razorpay/react-native-razorpay/pull/206) ([iamshadmirza](https://github.com/iamshadmirza))
- Added payout feature support [\#205](https://github.com/razorpay/react-native-razorpay/pull/205) ([pronav](https://github.com/pronav))

## [v2.1.25](https://github.com/razorpay/react-native-razorpay/tree/v2.1.25) (2019-07-15)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.21...v2.1.25)

**Closed issues:**

- Metro Bundler Is not Refreshing after adding "$\(PROJECT\_DIR\)/../node\_modules/react-native-razorpay/ios/" to the "framework search paths" in Build Settings   [\#199](https://github.com/razorpay/react-native-razorpay/issues/199)
- iOS payment.open always returns with error code 1.  [\#198](https://github.com/razorpay/react-native-razorpay/issues/198)
- Archive = IOS Razorpay bit code missing [\#197](https://github.com/razorpay/react-native-razorpay/issues/197)
- App get crash in iPad [\#195](https://github.com/razorpay/react-native-razorpay/issues/195)
- Code signing "Razorpay.framework" failed.\(React Native \) [\#194](https://github.com/razorpay/react-native-razorpay/issues/194)
- App crashes on launch with error dyld`\_\_abort\_with\_payload: [\#191](https://github.com/razorpay/react-native-razorpay/issues/191)
- FrameWork not found Razorpay [\#190](https://github.com/razorpay/react-native-razorpay/issues/190)
- Incompatible Swift version \(unknown ABI version 0x07\)  [\#189](https://github.com/razorpay/react-native-razorpay/issues/189)
- razorpay not working in iOS using Xcode 10 [\#187](https://github.com/razorpay/react-native-razorpay/issues/187)
- minSdkVersion 16 cannot be smaller than version 19 [\#183](https://github.com/razorpay/react-native-razorpay/issues/183)
- Breaks in production mode: Android [\#182](https://github.com/razorpay/react-native-razorpay/issues/182)
- New version fails too [\#179](https://github.com/razorpay/react-native-razorpay/issues/179)
- Not taking local Image path [\#178](https://github.com/razorpay/react-native-razorpay/issues/178)
- Build error Xcode 10.2: Incompatible Swift [\#177](https://github.com/razorpay/react-native-razorpay/issues/177)
- iOS App Thinning issue [\#176](https://github.com/razorpay/react-native-razorpay/issues/176)

**Merged pull requests:**

- fix: use react-native convention for version overrides, allows AndroidX to work [\#201](https://github.com/razorpay/react-native-razorpay/pull/201) ([mikehardy](https://github.com/mikehardy))

## [v2.0.21](https://github.com/razorpay/react-native-razorpay/tree/v2.0.21) (2019-04-16)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.20...v2.0.21)

**Closed issues:**

- Razorpay is getting crash [\#175](https://github.com/razorpay/react-native-razorpay/issues/175)
- Razorpay dyId: library not loaded: @rpath/libswiftCore.dylib [\#171](https://github.com/razorpay/react-native-razorpay/issues/171)
- Razorpay not compiling on xcode 10.2 [\#170](https://github.com/razorpay/react-native-razorpay/issues/170)
- ld: warning: Could not find auto-linked framework 'Razorpay' [\#169](https://github.com/razorpay/react-native-razorpay/issues/169)
- You should manually set the same version via Dependency Resolution [\#166](https://github.com/razorpay/react-native-razorpay/issues/166)
- Not able to open payment screen in release build in android [\#165](https://github.com/razorpay/react-native-razorpay/issues/165)
- dyld: Library not loaded: @rpath/libswiftCore.dylib error On Xcode 10.2 [\#163](https://github.com/razorpay/react-native-razorpay/issues/163)
- No response after payment success/failure [\#160](https://github.com/razorpay/react-native-razorpay/issues/160)
- Unable to link library in fresh project [\#156](https://github.com/razorpay/react-native-razorpay/issues/156)
- iOS Archiving "bitcode bundle" issue [\#154](https://github.com/razorpay/react-native-razorpay/issues/154)
-  java.lang.RuntimeException: Unable to start activity ComponentInfo{com.razorTest.razorpayMe/host.exp.exponent.MainActivity}: java.lang.IllegalArgumentException: unexpected url [\#153](https://github.com/razorpay/react-native-razorpay/issues/153)
- Native module can not be null. for ios  [\#141](https://github.com/razorpay/react-native-razorpay/issues/141)

**Merged pull requests:**

- iOS framework 1.1.1 released compiled in Swift 5 [\#173](https://github.com/razorpay/react-native-razorpay/pull/173) ([AbhinavArora95](https://github.com/AbhinavArora95))
- iOS Release 2.0.20 [\#155](https://github.com/razorpay/react-native-razorpay/pull/155) ([iThink32](https://github.com/iThink32))

## [v2.0.20](https://github.com/razorpay/react-native-razorpay/tree/v2.0.20) (2019-02-11)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.19...v2.0.20)

**Closed issues:**

- UnhandledPromiseRejectionWarning: Error: jest-haste-map: @providesModule naming collision: [\#150](https://github.com/razorpay/react-native-razorpay/issues/150)
- Razorpay sdk support for 32 bit IOS devices [\#148](https://github.com/razorpay/react-native-razorpay/issues/148)
-  Unable to start activity ComponentInfo{com.packageName/com.razorpay.CheckoutActivity} [\#140](https://github.com/razorpay/react-native-razorpay/issues/140)
- "JavaScript" in pop-up title [\#135](https://github.com/razorpay/react-native-razorpay/issues/135)
- Loading bank page stuck [\#113](https://github.com/razorpay/react-native-razorpay/issues/113)
- Stucked on the Loading Page - Android [\#109](https://github.com/razorpay/react-native-razorpay/issues/109)
- Yarn add causes error "expected hoisted manifest" [\#108](https://github.com/razorpay/react-native-razorpay/issues/108)

**Merged pull requests:**

- updated the examplemple folder and tested it [\#152](https://github.com/razorpay/react-native-razorpay/pull/152) ([iThink32](https://github.com/iThink32))
- iOS Release 2.0.19 [\#151](https://github.com/razorpay/react-native-razorpay/pull/151) ([iThink32](https://github.com/iThink32))

## [v2.0.19](https://github.com/razorpay/react-native-razorpay/tree/v2.0.19) (2019-01-27)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.18...v2.0.19)

**Closed issues:**

- Framework not found: RazorPay [\#149](https://github.com/razorpay/react-native-razorpay/issues/149)
- Archiving on iOS Failed - Several issues with the iOS integration [\#147](https://github.com/razorpay/react-native-razorpay/issues/147)
- Unable to open RazorpayCheckout when called from Modal in iOS - works in android [\#146](https://github.com/razorpay/react-native-razorpay/issues/146)
- TypeError: \_reactNativeRazorpay.default.open is not a function.  [\#145](https://github.com/razorpay/react-native-razorpay/issues/145)
- yarn add react-native-razorpay updates my project's yarn.lock [\#144](https://github.com/razorpay/react-native-razorpay/issues/144)
- Package Upgrade as per Google Policy [\#143](https://github.com/razorpay/react-native-razorpay/issues/143)
- Duplicate module name: react-native [\#139](https://github.com/razorpay/react-native-razorpay/issues/139)
- Razorpay iOS not working [\#43](https://github.com/razorpay/react-native-razorpay/issues/43)

## [v2.0.18](https://github.com/razorpay/react-native-razorpay/tree/v2.0.18) (2019-01-02)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.17...v2.0.18)

**Closed issues:**

- Subscription option? [\#142](https://github.com/razorpay/react-native-razorpay/issues/142)

## [v2.0.17](https://github.com/razorpay/react-native-razorpay/tree/v2.0.17) (2018-12-20)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.16...v2.0.17)

## [v2.0.16](https://github.com/razorpay/react-native-razorpay/tree/v2.0.16) (2018-12-03)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.14...v2.0.16)

**Closed issues:**

- Bundle error [\#138](https://github.com/razorpay/react-native-razorpay/issues/138)
- Update android permission --\> RECEIVE\_SMS [\#137](https://github.com/razorpay/react-native-razorpay/issues/137)
- Latest version crashing on iOS [\#136](https://github.com/razorpay/react-native-razorpay/issues/136)
- Don't check in node\_modules in example folder [\#133](https://github.com/razorpay/react-native-razorpay/issues/133)
- On EMI Option Screen is not scrollable UI Related Issues [\#132](https://github.com/razorpay/react-native-razorpay/issues/132)
- Duplicate module name: react-native [\#129](https://github.com/razorpay/react-native-razorpay/issues/129)
- After installation got the below error.can anyone  help?  [\#117](https://github.com/razorpay/react-native-razorpay/issues/117)
- Dyld Library Error. [\#83](https://github.com/razorpay/react-native-razorpay/issues/83)
- \[iOS\] broken build because of libRazorpayCheckout.a [\#63](https://github.com/razorpay/react-native-razorpay/issues/63)

**Merged pull requests:**

- iOS Release 2.0.14 [\#131](https://github.com/razorpay/react-native-razorpay/pull/131) ([iThink32](https://github.com/iThink32))

## [v2.0.14](https://github.com/razorpay/react-native-razorpay/tree/v2.0.14) (2018-10-09)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.13...v2.0.14)

**Closed issues:**

- Blank Window is shows while calling razorpay.open\(\) [\#130](https://github.com/razorpay/react-native-razorpay/issues/130)
- CodeSign issue with razorpay module [\#128](https://github.com/razorpay/react-native-razorpay/issues/128)
- Cannot read property 'buildConfigurationList' of undefined [\#127](https://github.com/razorpay/react-native-razorpay/issues/127)
- cocoa pod integration support added for iOS integration [\#126](https://github.com/razorpay/react-native-razorpay/issues/126)
- dyld: Symbol not found: \_$SBOWV [\#125](https://github.com/razorpay/react-native-razorpay/issues/125)
- Duplicate module name: react-animated [\#124](https://github.com/razorpay/react-native-razorpay/issues/124)
- Ionic ios app integrated with Razorpay - error code sign Razorpay.framework [\#123](https://github.com/razorpay/react-native-razorpay/issues/123)
- Suddenly : Code signing "Razorpay.framework" failed [\#122](https://github.com/razorpay/react-native-razorpay/issues/122)
- getting error undefined | undefined while calling RazorpayCheckout.open\(options\) [\#121](https://github.com/razorpay/react-native-razorpay/issues/121)
- dyld: Symbol not found: \_\_T0BOWV  [\#120](https://github.com/razorpay/react-native-razorpay/issues/120)
- while building the app grtting following error [\#118](https://github.com/razorpay/react-native-razorpay/issues/118)
- build errors [\#116](https://github.com/razorpay/react-native-razorpay/issues/116)
- XCODE 10 razorpay support [\#111](https://github.com/razorpay/react-native-razorpay/issues/111)

**Merged pull requests:**

- R/v2.0.13 [\#119](https://github.com/razorpay/react-native-razorpay/pull/119) ([iThink32](https://github.com/iThink32))

## [v2.0.13](https://github.com/razorpay/react-native-razorpay/tree/v2.0.13) (2018-09-30)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.10...v2.0.13)

**Merged pull requests:**

- iOS Release 2.0.10 [\#115](https://github.com/razorpay/react-native-razorpay/pull/115) ([iThink32](https://github.com/iThink32))

## [v2.0.10](https://github.com/razorpay/react-native-razorpay/tree/v2.0.10) (2018-09-28)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.9...v2.0.10)

**Closed issues:**

- React/RCTEventEmitter.h not found in iOS [\#112](https://github.com/razorpay/react-native-razorpay/issues/112)
- Module compiled with Swift 4.1 cannot be imported by the Swift 4.2 compiler: .../Framework/Razorpay.framework/Modules/Razorpay.swiftmodule/x86\_64.swiftmodule [\#110](https://github.com/razorpay/react-native-razorpay/issues/110)
- Card payment redirects to different page  [\#107](https://github.com/razorpay/react-native-razorpay/issues/107)
- dyld: Library not loaded: @rpath/libswiftCore.dylib  [\#106](https://github.com/razorpay/react-native-razorpay/issues/106)
- \[Android\] adding a 3 digit hex code crashes razor pay on android [\#105](https://github.com/razorpay/react-native-razorpay/issues/105)
- Razer payment react native---Cannot read property 'open' of undefined [\#104](https://github.com/razorpay/react-native-razorpay/issues/104)
- Application crash after closing the razorpay view [\#100](https://github.com/razorpay/react-native-razorpay/issues/100)
- Duplicate module name: react-animated [\#93](https://github.com/razorpay/react-native-razorpay/issues/93)
- "Invalid Segment Alignment. The app binary at '/Frameworks/Razorpay.framework/Razorpay' does not have proper segment alignment. [\#53](https://github.com/razorpay/react-native-razorpay/issues/53)
- Removing react native dependency from package.json  [\#42](https://github.com/razorpay/react-native-razorpay/issues/42)

**Merged pull requests:**

- iOS Release 2.0.9 [\#103](https://github.com/razorpay/react-native-razorpay/pull/103) ([iThink32](https://github.com/iThink32))

## [v2.0.9](https://github.com/razorpay/react-native-razorpay/tree/v2.0.9) (2018-08-20)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.8...v2.0.9)

## [v2.0.8](https://github.com/razorpay/react-native-razorpay/tree/v2.0.8) (2018-08-01)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.7...v2.0.8)

**Closed issues:**

- Need it working with Older version of RN -0.45, what changes I need to make ? [\#98](https://github.com/razorpay/react-native-razorpay/issues/98)
-  Cannot find module while installing [\#97](https://github.com/razorpay/react-native-razorpay/issues/97)
- RazorpayCheckout.open\(\) getting stuck [\#96](https://github.com/razorpay/react-native-razorpay/issues/96)
- Cannot read property 'buildConfigurationList' of undefined [\#95](https://github.com/razorpay/react-native-razorpay/issues/95)
- The SDK Build Tools revision \(24.0.0\) is too low for project ':react-native-razorpay'. Minimum required is 25.0.0 [\#92](https://github.com/razorpay/react-native-razorpay/issues/92)
- libRazorpaycheckout does not contain bitcode [\#88](https://github.com/razorpay/react-native-razorpay/issues/88)
- Is there a way to prevent bounces effect in iOS [\#86](https://github.com/razorpay/react-native-razorpay/issues/86)
- 'native module cannot be null' [\#22](https://github.com/razorpay/react-native-razorpay/issues/22)

**Merged pull requests:**

- Android sdk version updated [\#99](https://github.com/razorpay/react-native-razorpay/pull/99) ([hariramsvnit](https://github.com/hariramsvnit))
- release helper file moved [\#91](https://github.com/razorpay/react-native-razorpay/pull/91) ([iThink32](https://github.com/iThink32))
- modified .npmignore [\#90](https://github.com/razorpay/react-native-razorpay/pull/90) ([iThink32](https://github.com/iThink32))
- react-native version dependancy change [\#89](https://github.com/razorpay/react-native-razorpay/pull/89) ([iThink32](https://github.com/iThink32))

## [v2.0.7](https://github.com/razorpay/react-native-razorpay/tree/v2.0.7) (2018-06-11)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.6...v2.0.7)

**Closed issues:**

- Does it support Paytm wallet ? [\#85](https://github.com/razorpay/react-native-razorpay/issues/85)
-  NativeModules.RazorpayCheckout is undefined [\#54](https://github.com/razorpay/react-native-razorpay/issues/54)

**Merged pull requests:**

- sdk version updated to 2.0.7 and android SDK version updated to 1.4.8 [\#87](https://github.com/razorpay/react-native-razorpay/pull/87) ([hariramsvnit](https://github.com/hariramsvnit))
- iOS Release 2.0.6 [\#84](https://github.com/razorpay/react-native-razorpay/pull/84) ([iThink32](https://github.com/iThink32))

## [v2.0.6](https://github.com/razorpay/react-native-razorpay/tree/v2.0.6) (2018-06-05)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.5...v2.0.6)

**Closed issues:**

- Getting architechtural issues [\#75](https://github.com/razorpay/react-native-razorpay/issues/75)
- getting the following error while installing rules for eslint: [\#74](https://github.com/razorpay/react-native-razorpay/issues/74)
- Xcode 9.3 error but it is working in Xcode 9.2 [\#70](https://github.com/razorpay/react-native-razorpay/issues/70)

**Merged pull requests:**

- Script Optimization [\#82](https://github.com/razorpay/react-native-razorpay/pull/82) ([iThink32](https://github.com/iThink32))
- iOS Automation Script [\#80](https://github.com/razorpay/react-native-razorpay/pull/80) ([iThink32](https://github.com/iThink32))
- iOS release 2.0.5 readme update [\#73](https://github.com/razorpay/react-native-razorpay/pull/73) ([iThink32](https://github.com/iThink32))

## [v2.0.5](https://github.com/razorpay/react-native-razorpay/tree/v2.0.5) (2018-05-07)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.4...v2.0.5)

**Closed issues:**

- Something went wrong while linking. Error: Cannot read property 'buildConfigurationList' of undefined [\#68](https://github.com/razorpay/react-native-razorpay/issues/68)

**Merged pull requests:**

- iOS Release Version 2.0.5 [\#72](https://github.com/razorpay/react-native-razorpay/pull/72) ([iThink32](https://github.com/iThink32))
- Revert "iOS release for version 2.0.4" [\#71](https://github.com/razorpay/react-native-razorpay/pull/71) ([iThink32](https://github.com/iThink32))
- iOS release for version 2.0.4 [\#69](https://github.com/razorpay/react-native-razorpay/pull/69) ([iThink32](https://github.com/iThink32))
- iOS Swift4.1 Compatible Framework [\#66](https://github.com/razorpay/react-native-razorpay/pull/66) ([iThink32](https://github.com/iThink32))

## [v2.0.4](https://github.com/razorpay/react-native-razorpay/tree/v2.0.4) (2018-04-19)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.3...v2.0.4)

**Closed issues:**

- can you guys please make bitcode enabled cocoapod available. [\#67](https://github.com/razorpay/react-native-razorpay/issues/67)
- Different success objects on iOS and Android. Change documentation [\#40](https://github.com/razorpay/react-native-razorpay/issues/40)

## [v2.0.3](https://github.com/razorpay/react-native-razorpay/tree/v2.0.3) (2018-04-09)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.2...v2.0.3)

## [v2.0.2](https://github.com/razorpay/react-native-razorpay/tree/v2.0.2) (2018-04-06)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.1...v2.0.2)

**Closed issues:**

- How to integrate paytm wallet option with latest sdk [\#64](https://github.com/razorpay/react-native-razorpay/issues/64)
- Improvement: Please Update Documentation for iOS Integration [\#62](https://github.com/razorpay/react-native-razorpay/issues/62)
- Having ssl problem and sdk download link not working.  [\#56](https://github.com/razorpay/react-native-razorpay/issues/56)

**Merged pull requests:**

- plugin version updated to 2.0.1 [\#61](https://github.com/razorpay/react-native-razorpay/pull/61) ([hariramsvnit](https://github.com/hariramsvnit))

## [v2.0.1](https://github.com/razorpay/react-native-razorpay/tree/v2.0.1) (2018-03-16)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v2.0.0...v2.0.1)

**Closed issues:**

- Pod support for iOS [\#51](https://github.com/razorpay/react-native-razorpay/issues/51)

**Merged pull requests:**

- 2.0.0 Release - response of both ios and android are now in same format [\#59](https://github.com/razorpay/react-native-razorpay/pull/59) ([AbhinavArora95](https://github.com/AbhinavArora95))

## [v2.0.0](https://github.com/razorpay/react-native-razorpay/tree/v2.0.0) (2018-02-22)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v1.5.1...v2.0.0)

**Merged pull requests:**

- Flat response external wallet ios and removed unnecessary step in readme [\#58](https://github.com/razorpay/react-native-razorpay/pull/58) ([AbhinavArora95](https://github.com/AbhinavArora95))
- Android checkout SDK version updated [\#57](https://github.com/razorpay/react-native-razorpay/pull/57) ([hariramsvnit](https://github.com/hariramsvnit))
- Changed android wrapper to return flat response [\#52](https://github.com/razorpay/react-native-razorpay/pull/52) ([hariramsvnit](https://github.com/hariramsvnit))

## [v1.5.1](https://github.com/razorpay/react-native-razorpay/tree/v1.5.1) (2018-02-20)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/v1.4.2...v1.5.1)

**Closed issues:**

- com.razorpay.rn does not exist [\#55](https://github.com/razorpay/react-native-razorpay/issues/55)
- iOS Pay button does not work & Project does not build in xcode [\#50](https://github.com/razorpay/react-native-razorpay/issues/50)
- SDK link dead [\#49](https://github.com/razorpay/react-native-razorpay/issues/49)
- Razorpay is not working for android [\#48](https://github.com/razorpay/react-native-razorpay/issues/48)
- Latest iOS SDK has missing ExternalWalletSelectionProtocol.h [\#44](https://github.com/razorpay/react-native-razorpay/issues/44)

**Merged pull requests:**

- Swift wrapper [\#45](https://github.com/razorpay/react-native-razorpay/pull/45) ([nitinbizsingh](https://github.com/nitinbizsingh))

## [v1.4.2](https://github.com/razorpay/react-native-razorpay/tree/v1.4.2) (2018-01-04)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/1.2.0...v1.4.2)

**Closed issues:**

- UI breaks while focusing on TextInput [\#46](https://github.com/razorpay/react-native-razorpay/issues/46)
- Razorpay framework url download error [\#39](https://github.com/razorpay/react-native-razorpay/issues/39)
- Incompatible minsdk [\#36](https://github.com/razorpay/react-native-razorpay/issues/36)
- Exception '-\[Razorpay setExternalWalletSelectionDelegate:\]: unrecognized selector sent to instance 0x7ff38a6ea620' [\#32](https://github.com/razorpay/react-native-razorpay/issues/32)
- Android: Payment view got dismissed on App enters background. [\#31](https://github.com/razorpay/react-native-razorpay/issues/31)
- Razorpay Payment Window Not Open [\#29](https://github.com/razorpay/react-native-razorpay/issues/29)
- handle modal dismiss [\#28](https://github.com/razorpay/react-native-razorpay/issues/28)
- yarn add react-native-razorpay is not working [\#27](https://github.com/razorpay/react-native-razorpay/issues/27)
- Bit Code  [\#26](https://github.com/razorpay/react-native-razorpay/issues/26)
- Build Error [\#25](https://github.com/razorpay/react-native-razorpay/issues/25)
- Not working on ios after intallation [\#23](https://github.com/razorpay/react-native-razorpay/issues/23)
- Error During npm install [\#21](https://github.com/razorpay/react-native-razorpay/issues/21)
- BIT\_CODE error while archiving [\#20](https://github.com/razorpay/react-native-razorpay/issues/20)
- Problem in building package [\#19](https://github.com/razorpay/react-native-razorpay/issues/19)
- Not installed [\#18](https://github.com/razorpay/react-native-razorpay/issues/18)

**Merged pull requests:**

- android sdk version updated to 1.4.5 and plugin version updated to 1.4.2 [\#47](https://github.com/razorpay/react-native-razorpay/pull/47) ([hariramsvnit](https://github.com/hariramsvnit))
- \[readme\] readme file updated [\#41](https://github.com/razorpay/react-native-razorpay/pull/41) ([hariramsvnit](https://github.com/hariramsvnit))
- Android SDK version updated to 1.4.1 [\#38](https://github.com/razorpay/react-native-razorpay/pull/38) ([hariramsvnit](https://github.com/hariramsvnit))
- Update the build.gradle file [\#37](https://github.com/razorpay/react-native-razorpay/pull/37) ([harisvsulaiman](https://github.com/harisvsulaiman))
- \[sdk\_update\] checkout android sdk version updated to 1.4.0 [\#35](https://github.com/razorpay/react-native-razorpay/pull/35) ([hariramsvnit](https://github.com/hariramsvnit))
- Amend postinstall script to work on Windows 10 [\#33](https://github.com/razorpay/react-native-razorpay/pull/33) ([akshaybhalotia](https://github.com/akshaybhalotia))
- \[android\] Update Razorpay Android SDK to 1.3.3 [\#30](https://github.com/razorpay/react-native-razorpay/pull/30) ([mb-14](https://github.com/mb-14))

## [1.2.0](https://github.com/razorpay/react-native-razorpay/tree/1.2.0) (2017-03-08)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/1.1.1...1.2.0)

**Implemented enhancements:**

- razorpay react-native version 0.40 and above support [\#11](https://github.com/razorpay/react-native-razorpay/pull/11) ([sivakumar-cf](https://github.com/sivakumar-cf))

**Merged pull requests:**

- React Native version update [\#17](https://github.com/razorpay/react-native-razorpay/pull/17) ([akshaybhalotia](https://github.com/akshaybhalotia))
- \[android\] Make changes to support RN 0.40+ [\#16](https://github.com/razorpay/react-native-razorpay/pull/16) ([mb-14](https://github.com/mb-14))

## [1.1.1](https://github.com/razorpay/react-native-razorpay/tree/1.1.1) (2017-03-08)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/1.1.0...1.1.1)

**Merged pull requests:**

- iOS script changes [\#15](https://github.com/razorpay/react-native-razorpay/pull/15) ([pronav](https://github.com/pronav))

## [1.1.0](https://github.com/razorpay/react-native-razorpay/tree/1.1.0) (2017-03-07)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/1.0.1...1.1.0)

**Merged pull requests:**

- \[Urgent\] Orders API, External Wallet Support [\#13](https://github.com/razorpay/react-native-razorpay/pull/13) ([pronav](https://github.com/pronav))
- \[android\] Add support for external wallets and orders API flow [\#12](https://github.com/razorpay/react-native-razorpay/pull/12) ([mb-14](https://github.com/mb-14))

## [1.0.1](https://github.com/razorpay/react-native-razorpay/tree/1.0.1) (2016-12-27)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/1.0.0...1.0.1)

**Closed issues:**

- Not working with latest react-native 0.39  [\#8](https://github.com/razorpay/react-native-razorpay/issues/8)
- `npm install` is not working  [\#7](https://github.com/razorpay/react-native-razorpay/issues/7)
- Question: Is this production ready?  [\#6](https://github.com/razorpay/react-native-razorpay/issues/6)

**Merged pull requests:**

- Update Checkout Android SDK to v1.2.0, add compatibilty changes for RN 0.39 [\#9](https://github.com/razorpay/react-native-razorpay/pull/9) ([mb-14](https://github.com/mb-14))

## [1.0.0](https://github.com/razorpay/react-native-razorpay/tree/1.0.0) (2016-10-25)

[Full Changelog](https://github.com/razorpay/react-native-razorpay/compare/de63af2ba6fe829e7b7772930b6a039dd3eea2a7...1.0.0)

**Merged pull requests:**

- Makes README cleaner [\#5](https://github.com/razorpay/react-native-razorpay/pull/5) ([akshaybhalotia](https://github.com/akshaybhalotia))
- Make minor edits [\#4](https://github.com/razorpay/react-native-razorpay/pull/4) ([akshaybhalotia](https://github.com/akshaybhalotia))
- Promise based API [\#3](https://github.com/razorpay/react-native-razorpay/pull/3) ([akshaybhalotia](https://github.com/akshaybhalotia))
- Configure the repo for public release [\#2](https://github.com/razorpay/react-native-razorpay/pull/2) ([akshaybhalotia](https://github.com/akshaybhalotia))
- F/android [\#1](https://github.com/razorpay/react-native-razorpay/pull/1) ([mb-14](https://github.com/mb-14))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
