import { Platform, InteractionManager, Dimensions } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface MemoryInfo {
  used: number;
  total: number;
  available: number;
  percentage: number;
}

interface NetworkMetric {
  url: string;
  method: string;
  duration: number;
  size: number;
  status: number;
  timestamp: number;
}

interface RenderMetric {
  screen: string;
  renderTime: number;
  componentCount: number;
  timestamp: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private networkMetrics: NetworkMetric[] = [];
  private renderMetrics: RenderMetric[] = [];
  private isEnabled: boolean = true;
  private maxMetrics: number = 1000;
  private reportingInterval: number = 60000; // 1 minute
  private reportingTimer?: NodeJS.Timeout;

  constructor() {
    this.initializeMonitoring();
  }

  private async initializeMonitoring() {
    try {
      // Load previous metrics
      await this.loadMetrics();
      
      // Start periodic reporting
      this.startPeriodicReporting();
      
      // Monitor app state changes
      this.setupAppStateMonitoring();
    } catch (error) {
      console.error('Failed to initialize performance monitoring:', error);
    }
  }

  private async loadMetrics() {
    try {
      const stored = await AsyncStorage.getItem('performance_metrics');
      if (stored) {
        const data = JSON.parse(stored);
        this.metrics = data.metrics || [];
        this.networkMetrics = data.networkMetrics || [];
        this.renderMetrics = data.renderMetrics || [];
      }
    } catch (error) {
      console.error('Failed to load performance metrics:', error);
    }
  }

  private async saveMetrics() {
    try {
      const data = {
        metrics: this.metrics.slice(-this.maxMetrics),
        networkMetrics: this.networkMetrics.slice(-this.maxMetrics),
        renderMetrics: this.renderMetrics.slice(-this.maxMetrics),
      };
      await AsyncStorage.setItem('performance_metrics', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save performance metrics:', error);
    }
  }

  private startPeriodicReporting() {
    this.reportingTimer = setInterval(() => {
      this.collectSystemMetrics();
      this.saveMetrics();
    }, this.reportingInterval);
  }

  private setupAppStateMonitoring() {
    // Monitor app lifecycle events
    if (Platform.OS === 'ios') {
      // iOS specific monitoring
    } else {
      // Android specific monitoring
    }
  }

  // Core metric collection methods
  recordMetric(name: string, value: number, metadata?: Record<string, any>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata,
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  recordNetworkRequest(
    url: string,
    method: string,
    duration: number,
    size: number,
    status: number
  ) {
    if (!this.isEnabled) return;

    const metric: NetworkMetric = {
      url,
      method,
      duration,
      size,
      status,
      timestamp: Date.now(),
    };

    this.networkMetrics.push(metric);
    
    if (this.networkMetrics.length > this.maxMetrics) {
      this.networkMetrics = this.networkMetrics.slice(-this.maxMetrics);
    }

    // Record as general metric too
    this.recordMetric('network_request_duration', duration, {
      url,
      method,
      status,
      size,
    });
  }

  recordRenderTime(screen: string, renderTime: number, componentCount?: number) {
    if (!this.isEnabled) return;

    const metric: RenderMetric = {
      screen,
      renderTime,
      componentCount: componentCount || 0,
      timestamp: Date.now(),
    };

    this.renderMetrics.push(metric);
    
    if (this.renderMetrics.length > this.maxMetrics) {
      this.renderMetrics = this.renderMetrics.slice(-this.maxMetrics);
    }

    this.recordMetric('screen_render_time', renderTime, {
      screen,
      componentCount,
    });
  }

  // Timing utilities
  startTimer(name: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration);
      return duration;
    };
  }

  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, { success: true });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, { success: false, error: error.message });
      throw error;
    }
  }

  measureSync<T>(name: string, fn: () => T): T {
    const startTime = Date.now();
    try {
      const result = fn();
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, { success: true });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, { success: false, error: error.message });
      throw error;
    }
  }

  // System metrics collection
  private async collectSystemMetrics() {
    try {
      // Memory usage
      const memoryInfo = await this.getMemoryInfo();
      this.recordMetric('memory_usage_percentage', memoryInfo.percentage);
      this.recordMetric('memory_used_mb', memoryInfo.used / (1024 * 1024));

      // Screen dimensions
      const { width, height } = Dimensions.get('window');
      this.recordMetric('screen_width', width);
      this.recordMetric('screen_height', height);

      // Platform info
      this.recordMetric('platform_version', parseFloat(Platform.Version.toString()));
    } catch (error) {
      console.error('Failed to collect system metrics:', error);
    }
  }

  private async getMemoryInfo(): Promise<MemoryInfo> {
    // This is a simplified implementation
    // In a real app, you'd use native modules to get actual memory info
    return {
      used: 50 * 1024 * 1024, // 50MB
      total: 200 * 1024 * 1024, // 200MB
      available: 150 * 1024 * 1024, // 150MB
      percentage: 25, // 25%
    };
  }

  // Analysis methods
  getMetricsSummary(timeRange?: { start: number; end: number }) {
    const filteredMetrics = timeRange
      ? this.metrics.filter(m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end)
      : this.metrics;

    const summary: Record<string, { count: number; avg: number; min: number; max: number }> = {};

    filteredMetrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          count: 0,
          avg: 0,
          min: Infinity,
          max: -Infinity,
        };
      }

      const s = summary[metric.name];
      s.count++;
      s.min = Math.min(s.min, metric.value);
      s.max = Math.max(s.max, metric.value);
      s.avg = (s.avg * (s.count - 1) + metric.value) / s.count;
    });

    return summary;
  }

  getNetworkSummary() {
    const totalRequests = this.networkMetrics.length;
    const totalDuration = this.networkMetrics.reduce((sum, m) => sum + m.duration, 0);
    const totalSize = this.networkMetrics.reduce((sum, m) => sum + m.size, 0);
    const errorCount = this.networkMetrics.filter(m => m.status >= 400).length;

    return {
      totalRequests,
      averageDuration: totalRequests > 0 ? totalDuration / totalRequests : 0,
      totalSize,
      errorRate: totalRequests > 0 ? errorCount / totalRequests : 0,
      slowRequests: this.networkMetrics.filter(m => m.duration > 3000).length,
    };
  }

  getRenderSummary() {
    const totalRenders = this.renderMetrics.length;
    const totalTime = this.renderMetrics.reduce((sum, m) => sum + m.renderTime, 0);
    const slowRenders = this.renderMetrics.filter(m => m.renderTime > 100).length;

    const screenStats: Record<string, { count: number; avgTime: number }> = {};
    this.renderMetrics.forEach(metric => {
      if (!screenStats[metric.screen]) {
        screenStats[metric.screen] = { count: 0, avgTime: 0 };
      }
      const stats = screenStats[metric.screen];
      stats.count++;
      stats.avgTime = (stats.avgTime * (stats.count - 1) + metric.renderTime) / stats.count;
    });

    return {
      totalRenders,
      averageRenderTime: totalRenders > 0 ? totalTime / totalRenders : 0,
      slowRenders,
      slowRenderRate: totalRenders > 0 ? slowRenders / totalRenders : 0,
      screenStats,
    };
  }

  // Performance alerts
  checkPerformanceAlerts() {
    const alerts: string[] = [];
    const recentMetrics = this.metrics.filter(m => m.timestamp > Date.now() - 300000); // Last 5 minutes

    // Check for slow renders
    const recentRenders = this.renderMetrics.filter(m => m.timestamp > Date.now() - 300000);
    const slowRenders = recentRenders.filter(m => m.renderTime > 100);
    if (slowRenders.length > recentRenders.length * 0.3) {
      alerts.push('High number of slow renders detected');
    }

    // Check for network issues
    const recentNetwork = this.networkMetrics.filter(m => m.timestamp > Date.now() - 300000);
    const slowNetwork = recentNetwork.filter(m => m.duration > 5000);
    if (slowNetwork.length > recentNetwork.length * 0.2) {
      alerts.push('High number of slow network requests detected');
    }

    // Check memory usage
    const memoryMetrics = recentMetrics.filter(m => m.name === 'memory_usage_percentage');
    const highMemory = memoryMetrics.filter(m => m.value > 80);
    if (highMemory.length > memoryMetrics.length * 0.5) {
      alerts.push('High memory usage detected');
    }

    return alerts;
  }

  // Configuration
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  setMaxMetrics(max: number) {
    this.maxMetrics = max;
  }

  setReportingInterval(interval: number) {
    this.reportingInterval = interval;
    
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
    }
    this.startPeriodicReporting();
  }

  // Cleanup
  clearMetrics() {
    this.metrics = [];
    this.networkMetrics = [];
    this.renderMetrics = [];
    this.saveMetrics();
  }

  destroy() {
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
    }
    this.saveMetrics();
  }

  // Export data
  async exportMetrics() {
    return {
      metrics: this.metrics,
      networkMetrics: this.networkMetrics,
      renderMetrics: this.renderMetrics,
      summary: this.getMetricsSummary(),
      networkSummary: this.getNetworkSummary(),
      renderSummary: this.getRenderSummary(),
      alerts: this.checkPerformanceAlerts(),
      timestamp: Date.now(),
    };
  }
}

export default new PerformanceMonitor();
