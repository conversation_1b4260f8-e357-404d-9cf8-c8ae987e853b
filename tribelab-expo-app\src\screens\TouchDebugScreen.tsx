import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  Dimensions,
  ScrollView,
} from 'react-native';

const { width, height } = Dimensions.get('window');

const TouchDebugScreen = () => {
  const [touchCount, setTouchCount] = useState(0);
  const [inputText, setInputText] = useState('');
  const [lastTouch, setLastTouch] = useState('None');

  const handleTouch = (location: string) => {
    console.log(`TOUCH DEBUG: ${location} touched at ${new Date().toLocaleTimeString()}`);
    setTouchCount(prev => prev + 1);
    setLastTouch(location);
    Alert.alert('Touch Detected!', `${location} was touched successfully`);
  };

  return (
    <View style={styles.container}>
      {/* Debug Info Header */}
      <View style={styles.debugHeader}>
        <Text style={styles.debugText}>Touch Debug Mode</Text>
        <Text style={styles.debugText}>Screen: {width}x{height}</Text>
        <Text style={styles.debugText}>Touches: {touchCount}</Text>
        <Text style={styles.debugText}>Last: {lastTouch}</Text>
      </View>

      {/* Test Area 1: Simple Button */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Test 1: Basic Button</Text>
        <TouchableOpacity
          style={[styles.testButton, { backgroundColor: '#FF6B6B' }]}
          onPress={() => handleTouch('Red Button')}
          activeOpacity={0.7}
        >
          <Text style={styles.buttonText}>RED BUTTON</Text>
        </TouchableOpacity>
      </View>

      {/* Test Area 2: Different Z-Index */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Test 2: Z-Index Test</Text>
        <View style={styles.zIndexContainer}>
          <TouchableOpacity
            style={[styles.testButton, styles.zIndexButton1]}
            onPress={() => handleTouch('Z-Index Button 1')}
          >
            <Text style={styles.buttonText}>Z-INDEX 1</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.testButton, styles.zIndexButton2]}
            onPress={() => handleTouch('Z-Index Button 2')}
          >
            <Text style={styles.buttonText}>Z-INDEX 10</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Test Area 3: Input Fields */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Test 3: Input Field</Text>
        <TextInput
          style={styles.testInput}
          placeholder="Type here to test input"
          value={inputText}
          onChangeText={(text) => {
            console.log('TOUCH DEBUG: Input changed:', text);
            setInputText(text);
          }}
          onFocus={() => {
            console.log('TOUCH DEBUG: Input focused');
            setLastTouch('Input Field');
          }}
          onBlur={() => console.log('TOUCH DEBUG: Input blurred')}
        />
        <Text style={styles.inputDisplay}>Input: {inputText}</Text>
      </View>

      {/* Test Area 4: Absolute Positioning */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Test 4: Absolute Position</Text>
        <View style={styles.absoluteContainer}>
          <TouchableOpacity
            style={styles.absoluteButton}
            onPress={() => handleTouch('Absolute Button')}
          >
            <Text style={styles.buttonText}>ABSOLUTE</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Test Area 5: ScrollView Test */}
      <ScrollView style={styles.scrollSection} nestedScrollEnabled>
        <Text style={styles.sectionTitle}>Test 5: ScrollView</Text>
        <TouchableOpacity
          style={[styles.testButton, { backgroundColor: '#4ECDC4' }]}
          onPress={() => handleTouch('ScrollView Button')}
        >
          <Text style={styles.buttonText}>IN SCROLLVIEW</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    padding: 10,
  },
  debugHeader: {
    backgroundColor: '#343A40',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  debugText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'monospace',
  },
  testSection: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DEE2E6',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#495057',
  },
  testButton: {
    backgroundColor: '#007BFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 5,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  zIndexContainer: {
    height: 80,
    position: 'relative',
  },
  zIndexButton1: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 150,
    zIndex: 1,
    backgroundColor: '#FFC107',
  },
  zIndexButton2: {
    position: 'absolute',
    top: 20,
    left: 50,
    width: 150,
    zIndex: 10,
    backgroundColor: '#28A745',
  },
  testInput: {
    borderWidth: 2,
    borderColor: '#007BFF',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  inputDisplay: {
    marginTop: 5,
    fontSize: 14,
    color: '#6C757D',
  },
  absoluteContainer: {
    height: 60,
    position: 'relative',
    backgroundColor: '#F8F9FA',
  },
  absoluteButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#DC3545',
    padding: 10,
    borderRadius: 5,
  },
  scrollSection: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DEE2E6',
    maxHeight: 100,
  },
});

export default TouchDebugScreen;
