import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import Toast from 'react-native-toast-message';

interface SystemSettings {
  general: {
    siteName: string;
    siteDescription: string;
    maintenanceMode: boolean;
    registrationEnabled: boolean;
    emailVerificationRequired: boolean;
    maxFileUploadSize: number; // MB
  };
  security: {
    passwordMinLength: number;
    requireStrongPasswords: boolean;
    sessionTimeout: number; // minutes
    maxLoginAttempts: number;
    enableTwoFactor: boolean;
    allowedDomains: string[];
  };
  content: {
    autoModerationEnabled: boolean;
    profanityFilterEnabled: boolean;
    spamDetectionEnabled: boolean;
    maxPostLength: number;
    maxCommentLength: number;
    allowedFileTypes: string[];
  };
  notifications: {
    emailNotificationsEnabled: boolean;
    pushNotificationsEnabled: boolean;
    smsNotificationsEnabled: boolean;
    adminEmailAlerts: boolean;
    systemHealthAlerts: boolean;
  };
  payment: {
    stripeEnabled: boolean;
    paypalEnabled: boolean;
    razorpayEnabled: boolean;
    subscriptionTrialDays: number;
    refundPeriodDays: number;
    taxRate: number; // percentage
  };
}

const AdminSettingsScreen = () => {
  const navigation = useNavigation();
  
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeSection, setActiveSection] = useState<keyof SystemSettings>('general');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // Mock settings data - replace with real API call
      const mockSettings: SystemSettings = {
        general: {
          siteName: 'TribeLab',
          siteDescription: 'Build and monetize your community',
          maintenanceMode: false,
          registrationEnabled: true,
          emailVerificationRequired: true,
          maxFileUploadSize: 10,
        },
        security: {
          passwordMinLength: 8,
          requireStrongPasswords: true,
          sessionTimeout: 60,
          maxLoginAttempts: 5,
          enableTwoFactor: false,
          allowedDomains: [],
        },
        content: {
          autoModerationEnabled: true,
          profanityFilterEnabled: true,
          spamDetectionEnabled: true,
          maxPostLength: 5000,
          maxCommentLength: 1000,
          allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        },
        notifications: {
          emailNotificationsEnabled: true,
          pushNotificationsEnabled: true,
          smsNotificationsEnabled: false,
          adminEmailAlerts: true,
          systemHealthAlerts: true,
        },
        payment: {
          stripeEnabled: true,
          paypalEnabled: false,
          razorpayEnabled: false,
          subscriptionTrialDays: 7,
          refundPeriodDays: 30,
          taxRate: 18,
        },
      };
      
      setSettings(mockSettings);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load settings',
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      Toast.show({
        type: 'success',
        text1: 'Settings Saved',
        text2: 'System settings have been updated successfully',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Save Failed',
        text2: 'Failed to save settings',
      });
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    if (!settings) return;
    
    setSettings(prev => ({
      ...prev!,
      [section]: {
        ...prev![section],
        [key]: value,
      },
    }));
  };

  const renderSectionTabs = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsContainer}>
      {Object.keys(settings || {}).map((section) => (
        <TouchableOpacity
          key={section}
          style={[
            styles.tab,
            activeSection === section && styles.activeTab
          ]}
          onPress={() => setActiveSection(section as keyof SystemSettings)}
        >
          <Text style={[
            styles.tabText,
            activeSection === section && styles.activeTabText
          ]}>
            {section.charAt(0).toUpperCase() + section.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderGeneralSettings = () => (
    <View style={styles.section}>
      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Site Name</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.general.siteName}
          onChangeText={(value) => updateSetting('general', 'siteName', value)}
          placeholder="Enter site name"
        />
      </View>

      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Site Description</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          value={settings?.general.siteDescription}
          onChangeText={(value) => updateSetting('general', 'siteDescription', value)}
          placeholder="Enter site description"
          multiline
          numberOfLines={3}
        />
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Maintenance Mode</Text>
          <Switch
            value={settings?.general.maintenanceMode}
            onValueChange={(value) => updateSetting('general', 'maintenanceMode', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
        <Text style={styles.settingDescription}>
          When enabled, only admins can access the site
        </Text>
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Registration Enabled</Text>
          <Switch
            value={settings?.general.registrationEnabled}
            onValueChange={(value) => updateSetting('general', 'registrationEnabled', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Email Verification Required</Text>
          <Switch
            value={settings?.general.emailVerificationRequired}
            onValueChange={(value) => updateSetting('general', 'emailVerificationRequired', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
      </View>

      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Max File Upload Size (MB)</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.general.maxFileUploadSize.toString()}
          onChangeText={(value) => updateSetting('general', 'maxFileUploadSize', parseInt(value) || 0)}
          placeholder="10"
          keyboardType="numeric"
        />
      </View>
    </View>
  );

  const renderSecuritySettings = () => (
    <View style={styles.section}>
      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Password Minimum Length</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.security.passwordMinLength.toString()}
          onChangeText={(value) => updateSetting('security', 'passwordMinLength', parseInt(value) || 8)}
          placeholder="8"
          keyboardType="numeric"
        />
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Require Strong Passwords</Text>
          <Switch
            value={settings?.security.requireStrongPasswords}
            onValueChange={(value) => updateSetting('security', 'requireStrongPasswords', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
        <Text style={styles.settingDescription}>
          Require uppercase, lowercase, numbers, and special characters
        </Text>
      </View>

      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Session Timeout (minutes)</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.security.sessionTimeout.toString()}
          onChangeText={(value) => updateSetting('security', 'sessionTimeout', parseInt(value) || 60)}
          placeholder="60"
          keyboardType="numeric"
        />
      </View>

      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Max Login Attempts</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.security.maxLoginAttempts.toString()}
          onChangeText={(value) => updateSetting('security', 'maxLoginAttempts', parseInt(value) || 5)}
          placeholder="5"
          keyboardType="numeric"
        />
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Enable Two-Factor Authentication</Text>
          <Switch
            value={settings?.security.enableTwoFactor}
            onValueChange={(value) => updateSetting('security', 'enableTwoFactor', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
      </View>
    </View>
  );

  const renderContentSettings = () => (
    <View style={styles.section}>
      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Auto Moderation</Text>
          <Switch
            value={settings?.content.autoModerationEnabled}
            onValueChange={(value) => updateSetting('content', 'autoModerationEnabled', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
        <Text style={styles.settingDescription}>
          Automatically flag inappropriate content for review
        </Text>
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Profanity Filter</Text>
          <Switch
            value={settings?.content.profanityFilterEnabled}
            onValueChange={(value) => updateSetting('content', 'profanityFilterEnabled', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
      </View>

      <View style={styles.settingItem}>
        <View style={styles.switchRow}>
          <Text style={styles.settingLabel}>Spam Detection</Text>
          <Switch
            value={settings?.content.spamDetectionEnabled}
            onValueChange={(value) => updateSetting('content', 'spamDetectionEnabled', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
      </View>

      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Max Post Length</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.content.maxPostLength.toString()}
          onChangeText={(value) => updateSetting('content', 'maxPostLength', parseInt(value) || 5000)}
          placeholder="5000"
          keyboardType="numeric"
        />
      </View>

      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>Max Comment Length</Text>
        <TextInput
          style={styles.textInput}
          value={settings?.content.maxCommentLength.toString()}
          onChangeText={(value) => updateSetting('content', 'maxCommentLength', parseInt(value) || 1000)}
          placeholder="1000"
          keyboardType="numeric"
        />
      </View>
    </View>
  );

  const renderActiveSection = () => {
    if (!settings) return null;

    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'security':
        return renderSecuritySettings();
      case 'content':
        return renderContentSettings();
      case 'notifications':
        return (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notification Settings</Text>
            <Text style={styles.comingSoon}>Coming soon...</Text>
          </View>
        );
      case 'payment':
        return (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Payment Settings</Text>
            <Text style={styles.comingSoon}>Coming soon...</Text>
          </View>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Admin Settings</Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={saveSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <Text style={styles.saveButtonText}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Section Tabs */}
      {renderSectionTabs()}

      {/* Content */}
      <ScrollView style={styles.content}>
        {renderActiveSection()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  tabsContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tab: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  settingItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  comingSoon: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 40,
  },
});

export default AdminSettingsScreen;
