import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  FlatList,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import paymentService from '../services/payment';
import Toast from 'react-native-toast-message';

interface Invoice {
  id: string;
  number: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'overdue' | 'cancelled';
  dueDate: string;
  paidDate?: string;
  createdAt: string;
  description: string;
  subscriptionId?: string;
  downloadUrl?: string;
  items: {
    description: string;
    amount: number;
    quantity: number;
  }[];
}

const InvoicesScreen = () => {
  const navigation = useNavigation();
  
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [downloadingInvoice, setDownloadingInvoice] = useState<string | null>(null);

  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      
      const invoicesData = await paymentService.getInvoices();
      setInvoices(invoicesData);
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load invoices',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const downloadInvoice = async (invoice: Invoice) => {
    try {
      setDownloadingInvoice(invoice.id);
      
      if (invoice.downloadUrl) {
        // Open the existing download URL
        const supported = await Linking.canOpenURL(invoice.downloadUrl);
        if (supported) {
          await Linking.openURL(invoice.downloadUrl);
          Toast.show({
            type: 'success',
            text1: 'Invoice Opened',
            text2: 'Invoice opened in your browser',
          });
        } else {
          throw new Error('Cannot open invoice URL');
        }
      } else {
        // Request new download URL
        const downloadUrl = await paymentService.downloadInvoice(invoice.id);
        if (downloadUrl) {
          const supported = await Linking.canOpenURL(downloadUrl);
          if (supported) {
            await Linking.openURL(downloadUrl);
            Toast.show({
              type: 'success',
              text1: 'Invoice Downloaded',
              text2: 'Invoice download started',
            });
          } else {
            throw new Error('Cannot open download URL');
          }
        } else {
          throw new Error('Download URL not available');
        }
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Download Failed',
        text2: 'Unable to download invoice',
      });
    } finally {
      setDownloadingInvoice(null);
    }
  };

  const shareInvoice = (invoice: Invoice) => {
    Alert.alert(
      'Share Invoice',
      `Share invoice ${invoice.number}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Share',
          onPress: () => {
            // Implement sharing functionality
            Toast.show({
              type: 'info',
              text1: 'Share Feature',
              text2: 'Sharing functionality will be implemented',
            });
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return '#28a745';
      case 'pending':
        return '#ffc107';
      case 'overdue':
        return '#dc3545';
      case 'cancelled':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'overdue':
        return 'warning';
      case 'cancelled':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderInvoice = ({ item: invoice }: { item: Invoice }) => (
    <View style={styles.invoiceCard}>
      <View style={styles.invoiceHeader}>
        <View style={styles.invoiceInfo}>
          <Text style={styles.invoiceNumber}>Invoice #{invoice.number}</Text>
          <Text style={styles.invoiceDescription}>{invoice.description}</Text>
          <Text style={styles.invoiceDate}>
            Created: {formatDate(invoice.createdAt)}
          </Text>
          {invoice.dueDate && (
            <Text style={styles.dueDate}>
              Due: {formatDate(invoice.dueDate)}
            </Text>
          )}
          {invoice.paidDate && (
            <Text style={styles.paidDate}>
              Paid: {formatDate(invoice.paidDate)}
            </Text>
          )}
        </View>
        
        <View style={styles.invoiceAmount}>
          <Text style={styles.amountText}>
            {paymentService.formatCurrency(invoice.amount, invoice.currency)}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(invoice.status) }]}>
            <Icon name={getStatusIcon(invoice.status)} size={12} color="#fff" />
            <Text style={styles.statusText}>
              {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>

      {/* Invoice items */}
      {invoice.items && invoice.items.length > 0 && (
        <View style={styles.invoiceItems}>
          <Text style={styles.itemsTitle}>Items:</Text>
          {invoice.items.map((item, index) => (
            <View key={index} style={styles.invoiceItem}>
              <Text style={styles.itemDescription}>{item.description}</Text>
              <Text style={styles.itemAmount}>
                {item.quantity}x {paymentService.formatCurrency(item.amount, invoice.currency)}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => downloadInvoice(invoice)}
          disabled={downloadingInvoice === invoice.id}
        >
          {downloadingInvoice === invoice.id ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <Icon name="download" size={16} color="#007AFF" />
          )}
          <Text style={styles.actionButtonText}>
            {downloadingInvoice === invoice.id ? 'Downloading...' : 'Download'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.shareButton]}
          onPress={() => shareInvoice(invoice)}
        >
          <Icon name="share" size={16} color="#28a745" />
          <Text style={[styles.actionButtonText, styles.shareButtonText]}>Share</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Invoices</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={() => loadInvoices(true)}
        >
          <Icon name="refresh" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading invoices...</Text>
        </View>
      ) : (
        <FlatList
          data={invoices}
          renderItem={renderInvoice}
          keyExtractor={(item) => item.id}
          style={styles.invoicesList}
          contentContainerStyle={styles.invoicesContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={() => loadInvoices(true)} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Icon name="receipt-outline" size={64} color="#ccc" />
              <Text style={styles.emptyTitle}>No Invoices</Text>
              <Text style={styles.emptyText}>
                Your invoices will appear here when available
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  invoicesList: {
    flex: 1,
  },
  invoicesContent: {
    padding: 20,
  },
  invoiceCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  invoiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  invoiceInfo: {
    flex: 1,
    marginRight: 12,
  },
  invoiceNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  invoiceDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  invoiceDate: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
  dueDate: {
    fontSize: 12,
    color: '#dc3545',
    marginBottom: 2,
  },
  paidDate: {
    fontSize: 12,
    color: '#28a745',
  },
  invoiceAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  invoiceItems: {
    marginBottom: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  itemsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  invoiceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  itemAmount: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    gap: 6,
    flex: 1,
    justifyContent: 'center',
  },
  shareButton: {
    borderColor: '#28a745',
  },
  actionButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  shareButtonText: {
    color: '#28a745',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default InvoicesScreen;
