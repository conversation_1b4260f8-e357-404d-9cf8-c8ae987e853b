import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import { Platform } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { store } from "../store";
import { addNotification } from "../store/slices/notificationSlice";

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  private expoPushToken: string | null = null;

  async initialize() {
    try {
      // Register for push notifications
      await this.registerForPushNotifications();

      // Set up notification listeners
      this.setupNotificationListeners();

      console.log("Notification service initialized");
    } catch (error) {
      console.error("Failed to initialize notification service:", error);
    }
  }

  private async registerForPushNotifications() {
    if (!Device.isDevice) {
      console.log("Must use physical device for Push Notifications");
      return;
    }

    // Check existing permissions
    const { status: existingStatus } =
      await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    // Request permissions if not granted
    if (existingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== "granted") {
      console.log("Failed to get push token for push notification!");
      return;
    }

    // Get the push token
    try {
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: "your-expo-project-id", // Replace with your actual project ID
      });

      this.expoPushToken = token.data;
      console.log("Expo push token:", this.expoPushToken);

      // Store token locally
      await AsyncStorage.setItem("expo_push_token", this.expoPushToken);

      // Send token to your server
      await this.sendTokenToServer(this.expoPushToken);
    } catch (error) {
      console.error("Failed to get push token:", error);
    }

    // Configure notification channel for Android
    if (Platform.OS === "android") {
      await Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
    }
  }

  private async sendTokenToServer(token: string) {
    try {
      // Send the token to your backend
      // This should be implemented based on your API
      console.log("Sending push token to server:", token);

      // Example API call:
      // await api.post('/user/push-token', { token });
    } catch (error) {
      console.error("Failed to send push token to server:", error);
    }
  }

  private setupNotificationListeners() {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      console.log("Notification received:", notification);

      // Add to Redux store
      const notificationData = {
        id: notification.request.identifier,
        title: notification.request.content.title || "",
        message: notification.request.content.body || "",
        data: notification.request.content.data,
        isRead: false,
        createdAt: new Date().toISOString(),
        type: notification.request.content.data?.type || "system",
        priority: "medium" as const,
        user: {
          id: "system",
          name: "System",
        },
      };

      store.dispatch(addNotification(notificationData));
    });

    // Handle notification tapped/opened
    Notifications.addNotificationResponseReceivedListener((response) => {
      console.log("Notification response:", response);

      const data = response.notification.request.content.data;

      // Handle navigation based on notification data
      this.handleNotificationNavigation(data);
    });
  }

  private handleNotificationNavigation(data: any) {
    // Implement navigation logic based on notification type
    console.log("Handling notification navigation:", data);

    // Example navigation logic:
    // if (data.type === 'message') {
    //   // Navigate to chat screen
    // } else if (data.type === 'community') {
    //   // Navigate to community screen
    // }
  }

  async scheduleLocalNotification(
    title: string,
    body: string,
    data?: any,
    delay: number = 0
  ) {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: true,
        },
        trigger: delay > 0 ? { seconds: delay } : null,
      });

      console.log("Local notification scheduled:", identifier);
      return identifier;
    } catch (error) {
      console.error("Failed to schedule local notification:", error);
      return null;
    }
  }

  async cancelNotification(identifier: string) {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log("Notification cancelled:", identifier);
    } catch (error) {
      console.error("Failed to cancel notification:", error);
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log("All notifications cancelled");
    } catch (error) {
      console.error("Failed to cancel all notifications:", error);
    }
  }

  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error("Failed to set badge count:", error);
    }
  }

  async clearBadge() {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error("Failed to clear badge:", error);
    }
  }

  getPushToken(): string | null {
    return this.expoPushToken;
  }

  async getStoredPushToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem("expo_push_token");
    } catch (error) {
      console.error("Failed to get stored push token:", error);
      return null;
    }
  }

  // Enhanced notification methods
  async sendCommunityNotification(
    communityId: string,
    title: string,
    body: string,
    data?: any
  ) {
    await this.scheduleLocalNotification(title, body, {
      type: "community",
      communityId,
      ...data,
    });
  }

  async sendCourseNotification(
    courseId: string,
    title: string,
    body: string,
    data?: any
  ) {
    await this.scheduleLocalNotification(title, body, {
      type: "course",
      courseId,
      ...data,
    });
  }

  async sendMessageNotification(
    senderId: string,
    senderName: string,
    message: string,
    chatId?: string
  ) {
    await this.scheduleLocalNotification(
      `New message from ${senderName}`,
      message,
      { type: "message", senderId, chatId }
    );
  }

  async sendPaymentNotification(title: string, body: string, data?: any) {
    await this.scheduleLocalNotification(title, body, {
      type: "payment",
      ...data,
    });
  }

  async sendSystemNotification(title: string, body: string, data?: any) {
    await this.scheduleLocalNotification(title, body, {
      type: "system",
      ...data,
    });
  }

  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error("Failed to get badge count:", error);
      return 0;
    }
  }

  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error("Failed to set badge count:", error);
    }
  }

  async clearBadge() {
    await this.setBadgeCount(0);
  }

  async getAllScheduledNotifications() {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error("Failed to get scheduled notifications:", error);
      return [];
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log("All notifications cancelled");
    } catch (error) {
      console.error("Failed to cancel all notifications:", error);
    }
  }

  async getNotificationCategories() {
    try {
      if (Platform.OS === "ios") {
        return await Notifications.getNotificationCategoriesAsync();
      }
      return [];
    } catch (error) {
      console.error("Failed to get notification categories:", error);
      return [];
    }
  }

  async setNotificationCategories() {
    try {
      if (Platform.OS === "ios") {
        await Notifications.setNotificationCategoriesAsync([
          {
            identifier: "message",
            actions: [
              {
                identifier: "reply",
                buttonTitle: "Reply",
                textInput: {
                  submitButtonTitle: "Send",
                  placeholder: "Type your reply...",
                },
              },
              {
                identifier: "mark_read",
                buttonTitle: "Mark as Read",
              },
            ],
          },
          {
            identifier: "community",
            actions: [
              {
                identifier: "view",
                buttonTitle: "View",
              },
              {
                identifier: "dismiss",
                buttonTitle: "Dismiss",
              },
            ],
          },
        ]);
      }
    } catch (error) {
      console.error("Failed to set notification categories:", error);
    }
  }

  // Test notification for development
  async sendTestNotification() {
    await this.scheduleLocalNotification(
      "Test Notification",
      "This is a test notification from TribeLab",
      { type: "test" },
      2 // 2 seconds delay
    );
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
