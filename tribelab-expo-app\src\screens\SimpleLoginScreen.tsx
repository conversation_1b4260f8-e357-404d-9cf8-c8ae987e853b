import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
} from 'react-native';

const SimpleLoginScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>TribeLab Login</Text>
      
      <TouchableOpacity 
        style={styles.button} 
        onPress={() => {
          console.log('SIMPLE: Button pressed!');
          Alert.alert('Success!', 'Touch is working!');
        }}
        activeOpacity={0.7}
      >
        <Text style={styles.buttonText}>Test Button</Text>
      </TouchableOpacity>

      <TextInput
        style={styles.input}
        placeholder="Test email input"
        onChangeText={(text) => console.log('SIMPLE: Input changed:', text)}
        onFocus={() => console.log('SIMPLE: Input focused')}
        onBlur={() => console.log('SIMPLE: Input blurred')}
      />

      <TextInput
        style={styles.input}
        placeholder="Test password input"
        secureTextEntry
        onChangeText={(text) => console.log('SIMPLE: Password changed:', text)}
        onFocus={() => console.log('SIMPLE: Password focused')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 40,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 30,
    width: '100%',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    marginBottom: 20,
    backgroundColor: '#fff',
  },
});

export default SimpleLoginScreen;
