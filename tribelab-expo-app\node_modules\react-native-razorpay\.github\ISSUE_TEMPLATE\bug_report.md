---
name: "🐛 Bug Report"
about: Report a reproducible bug or regression in React Native Razorpay Plugin.
title: ''
labels: 'Needs: Triage :mag:'

---

<!--
------ 👆 Click "Preview"!

HI! PLEASE STOP TO READ THIS!! IF YOU DO NOT FOLLOW THE INSTRUCTIONS, YOUR ISSUE
WILL LIKELY BE CLOSED.

* Please fill out this template with all the relevant information so we can understand what's going on and fix the issue. We appreciate bugs filed and PRs submitted!

-->

## Description
Please provide a clear and concise description of what the bug is. Include screenshots if needed.
Please test using the latest React Native Razorpay plugin release to make sure your issue has not already been fixed. 

* **Specific to iOS Users :-**
- [ ] I have tried updating Razorpay pod to the latest version by using 'pod update'.

## Razorpay Package Version :
Open `Package.json`. > Copy `react-native-razorpay` version here. 

## Xcode Version (iOS) :
Open Xcode > Go to `About Xcode` > copy the Xcode version here.

## Razorpay-pod version (iOS) :
Go to your project path > Go to folder named `ios`  > open 'podfile.lock' file > search for 'razorpay-pod' > copy the line here 

## Java and Gradle Version (android) :
Specify your Java and Gradle version.

### What you did:

<!-- What you were doing -->

### What happened:

<!-- Please provide the full error message/screenshots/anything -->

## Steps To Reproduce
Provide a detailed list of steps that reproduce the issue.

1.
2.

### Suggested solution:

<!--
It's ok if you don't have a suggested solution, but it really helps if you could
do a little digging to come up with some suggestions on how to improve things.
-->

## Code example, screenshot, or link to a repository:
Please provide a link to a repository on GitHub, or provide a minimal code example that reproduces the problem.
You may provide a screenshot of the application if you think it is relevant to your bug report.
Here are some tips for providing a minimal example: https://stackoverflow.com/help/mcve

