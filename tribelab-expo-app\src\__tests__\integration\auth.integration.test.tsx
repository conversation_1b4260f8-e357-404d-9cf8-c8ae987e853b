import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { render, mockDataGenerators, testHelpers } from '../utils/testUtils';
import LoginScreen from '../../screens/LoginScreen';
import RegisterScreen from '../../screens/RegisterScreen';
import authService from '../../services/auth';

// Mock the auth service
jest.mock('../../services/auth');
const mockAuthService = authService as jest.Mocked<typeof authService>;

// Mock Toast
jest.mock('react-native-toast-message', () => ({
  show: jest.fn(),
}));

describe('Authentication Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset fetch mock
    (global.fetch as jest.Mock).mockClear();
  });

  describe('Login Flow', () => {
    it('successfully logs in a user with valid credentials', async () => {
      const mockUser = mockDataGenerators.user();
      const mockToken = 'mock-jwt-token';

      // Mock successful login response
      mockAuthService.login.mockResolvedValue({
        user: mockUser,
        token: mockToken,
      });

      const { getByTestId, getByText } = render(<LoginScreen />);

      // Fill in login form
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const loginButton = getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(loginButton);

      // Wait for login to complete
      await waitFor(() => {
        expect(mockAuthService.login).toHaveBeenCalledWith(
          '<EMAIL>',
          'password123'
        );
      });

      // Verify success state
      expect(getByText('Welcome back!')).toBeTruthy();
    });

    it('shows error message for invalid credentials', async () => {
      // Mock failed login response
      mockAuthService.login.mockRejectedValue(new Error('Invalid credentials'));

      const { getByTestId, getByText } = render(<LoginScreen />);

      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const loginButton = getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'wrongpassword');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(getByText('Invalid credentials')).toBeTruthy();
      });
    });

    it('validates email format before submission', async () => {
      const { getByTestId, getByText } = render(<LoginScreen />);

      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const loginButton = getByText('Sign In');

      fireEvent.changeText(emailInput, 'invalid-email');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(getByText('Please enter a valid email address')).toBeTruthy();
      });

      // Should not call login service
      expect(mockAuthService.login).not.toHaveBeenCalled();
    });

    it('shows loading state during login', async () => {
      // Mock delayed login response
      mockAuthService.login.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          user: mockDataGenerators.user(),
          token: 'token',
        }), 100))
      );

      const { getByTestId, getByText } = render(<LoginScreen />);

      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const loginButton = getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(loginButton);

      // Should show loading state
      expect(getByTestId('login-loading')).toBeTruthy();
      expect(getByText('Signing In...')).toBeTruthy();

      await waitFor(() => {
        expect(mockAuthService.login).toHaveBeenCalled();
      });
    });
  });

  describe('Registration Flow', () => {
    it('successfully registers a new user', async () => {
      const mockUser = mockDataGenerators.user();
      
      mockAuthService.register.mockResolvedValue({
        user: mockUser,
        token: 'mock-token',
      });

      const { getByTestId, getByText } = render(<RegisterScreen />);

      // Fill registration form
      const nameInput = getByTestId('name-input');
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const confirmPasswordInput = getByTestId('confirm-password-input');
      const registerButton = getByText('Create Account');

      fireEvent.changeText(nameInput, 'Test User');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'password123');
      fireEvent.press(registerButton);

      await waitFor(() => {
        expect(mockAuthService.register).toHaveBeenCalledWith({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123',
        });
      });
    });

    it('validates password confirmation', async () => {
      const { getByTestId, getByText } = render(<RegisterScreen />);

      const nameInput = getByTestId('name-input');
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const confirmPasswordInput = getByTestId('confirm-password-input');
      const registerButton = getByText('Create Account');

      fireEvent.changeText(nameInput, 'Test User');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'differentpassword');
      fireEvent.press(registerButton);

      await waitFor(() => {
        expect(getByText('Passwords do not match')).toBeTruthy();
      });

      expect(mockAuthService.register).not.toHaveBeenCalled();
    });

    it('validates password strength', async () => {
      const { getByTestId, getByText } = render(<RegisterScreen />);

      const passwordInput = getByTestId('password-input');
      fireEvent.changeText(passwordInput, '123');

      await waitFor(() => {
        expect(getByText('Password must be at least 8 characters')).toBeTruthy();
      });
    });

    it('handles registration errors', async () => {
      mockAuthService.register.mockRejectedValue(new Error('Email already exists'));

      const { getByTestId, getByText } = render(<RegisterScreen />);

      const nameInput = getByTestId('name-input');
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const confirmPasswordInput = getByTestId('confirm-password-input');
      const registerButton = getByText('Create Account');

      fireEvent.changeText(nameInput, 'Test User');
      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.changeText(confirmPasswordInput, 'password123');
      fireEvent.press(registerButton);

      await waitFor(() => {
        expect(getByText('Email already exists')).toBeTruthy();
      });
    });
  });

  describe('Social Authentication', () => {
    it('handles Google sign-in', async () => {
      const mockUser = mockDataGenerators.user();
      
      mockAuthService.signInWithGoogle.mockResolvedValue({
        user: mockUser,
        token: 'google-token',
      });

      const { getByTestId } = render(<LoginScreen />);

      const googleButton = getByTestId('google-signin-button');
      fireEvent.press(googleButton);

      await waitFor(() => {
        expect(mockAuthService.signInWithGoogle).toHaveBeenCalled();
      });
    });

    it('handles Apple sign-in', async () => {
      const mockUser = mockDataGenerators.user();
      
      mockAuthService.signInWithApple.mockResolvedValue({
        user: mockUser,
        token: 'apple-token',
      });

      const { getByTestId } = render(<LoginScreen />);

      const appleButton = getByTestId('apple-signin-button');
      fireEvent.press(appleButton);

      await waitFor(() => {
        expect(mockAuthService.signInWithApple).toHaveBeenCalled();
      });
    });

    it('handles social sign-in errors', async () => {
      mockAuthService.signInWithGoogle.mockRejectedValue(
        new Error('Google sign-in cancelled')
      );

      const { getByTestId, getByText } = render(<LoginScreen />);

      const googleButton = getByTestId('google-signin-button');
      fireEvent.press(googleButton);

      await waitFor(() => {
        expect(getByText('Google sign-in cancelled')).toBeTruthy();
      });
    });
  });

  describe('Password Reset Flow', () => {
    it('sends password reset email', async () => {
      mockAuthService.resetPassword.mockResolvedValue({ success: true });

      const { getByTestId, getByText } = render(<LoginScreen />);

      // Navigate to forgot password
      const forgotPasswordLink = getByText('Forgot Password?');
      fireEvent.press(forgotPasswordLink);

      const emailInput = getByTestId('reset-email-input');
      const resetButton = getByText('Send Reset Email');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(resetButton);

      await waitFor(() => {
        expect(mockAuthService.resetPassword).toHaveBeenCalledWith('<EMAIL>');
      });

      expect(getByText('Reset email sent successfully')).toBeTruthy();
    });

    it('validates email for password reset', async () => {
      const { getByTestId, getByText } = render(<LoginScreen />);

      const forgotPasswordLink = getByText('Forgot Password?');
      fireEvent.press(forgotPasswordLink);

      const emailInput = getByTestId('reset-email-input');
      const resetButton = getByText('Send Reset Email');

      fireEvent.changeText(emailInput, 'invalid-email');
      fireEvent.press(resetButton);

      await waitFor(() => {
        expect(getByText('Please enter a valid email address')).toBeTruthy();
      });

      expect(mockAuthService.resetPassword).not.toHaveBeenCalled();
    });
  });

  describe('Biometric Authentication', () => {
    it('enables biometric authentication after login', async () => {
      const mockUser = mockDataGenerators.user();
      
      mockAuthService.login.mockResolvedValue({
        user: mockUser,
        token: 'token',
      });

      mockAuthService.isBiometricAvailable.mockResolvedValue(true);

      const { getByTestId, getByText } = render(<LoginScreen />);

      // Complete login first
      const emailInput = getByTestId('email-input');
      const passwordInput = getByTestId('password-input');
      const loginButton = getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(loginButton);

      await waitFor(() => {
        expect(getByText('Enable Biometric Login?')).toBeTruthy();
      });

      const enableBiometricButton = getByText('Enable');
      fireEvent.press(enableBiometricButton);

      await waitFor(() => {
        expect(mockAuthService.enableBiometric).toHaveBeenCalled();
      });
    });

    it('authenticates with biometrics', async () => {
      const mockUser = mockDataGenerators.user();
      
      mockAuthService.authenticateWithBiometric.mockResolvedValue({
        user: mockUser,
        token: 'biometric-token',
      });

      const { getByTestId } = render(<LoginScreen />);

      const biometricButton = getByTestId('biometric-login-button');
      fireEvent.press(biometricButton);

      await waitFor(() => {
        expect(mockAuthService.authenticateWithBiometric).toHaveBeenCalled();
      });
    });
  });

  describe('Session Management', () => {
    it('handles token expiration', async () => {
      // Mock expired token response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({ error: 'Token expired' }),
      });

      mockAuthService.refreshToken.mockResolvedValue({
        token: 'new-token',
      });

      // Simulate an API call that triggers token refresh
      const { getByTestId } = render(<LoginScreen />);
      
      // This would typically be triggered by an API call
      await waitFor(() => {
        expect(mockAuthService.refreshToken).toHaveBeenCalled();
      });
    });

    it('logs out user when refresh token is invalid', async () => {
      mockAuthService.refreshToken.mockRejectedValue(new Error('Invalid refresh token'));

      const { getByText } = render(<LoginScreen />);

      await waitFor(() => {
        expect(mockAuthService.logout).toHaveBeenCalled();
      });

      expect(getByText('Session expired. Please log in again.')).toBeTruthy();
    });
  });
});
