import { AppState, AppStateStatus } from 'react-native';
import performanceMonitor from '../services/performance';
import cacheService from '../services/cache';

interface MemoryManagerConfig {
  enableAutoCleanup: boolean;
  memoryThreshold: number; // Percentage
  cleanupInterval: number; // Milliseconds
  enableImageOptimization: boolean;
  maxImageCacheSize: number; // Bytes
}

class MemoryManager {
  private config: MemoryManagerConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private appStateSubscription?: any;
  private memoryWarningListeners: Array<() => void> = [];
  private imageCache: Map<string, { data: any; size: number; lastAccessed: number }> = new Map();
  private totalImageCacheSize: number = 0;

  constructor() {
    this.config = {
      enableAutoCleanup: true,
      memoryThreshold: 80, // 80%
      cleanupInterval: 30000, // 30 seconds
      enableImageOptimization: true,
      maxImageCacheSize: 20 * 1024 * 1024, // 20MB
    };

    this.initialize();
  }

  private initialize() {
    this.setupAppStateListener();
    this.startAutoCleanup();
    this.setupMemoryWarningListener();
  }

  private setupAppStateListener() {
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  private handleAppStateChange(nextAppState: AppStateStatus) {
    if (nextAppState === 'background') {
      // App went to background - aggressive cleanup
      this.performAggressiveCleanup();
    } else if (nextAppState === 'active') {
      // App became active - light cleanup
      this.performLightCleanup();
    }
  }

  private startAutoCleanup() {
    if (!this.config.enableAutoCleanup) return;

    this.cleanupTimer = setInterval(() => {
      this.checkMemoryUsage();
    }, this.config.cleanupInterval);
  }

  private setupMemoryWarningListener() {
    // In a real implementation, you'd use native modules to listen for memory warnings
    // This is a simulation
    setInterval(() => {
      this.simulateMemoryCheck();
    }, 10000); // Check every 10 seconds
  }

  private async simulateMemoryCheck() {
    // Simulate memory usage check
    const memoryUsage = Math.random() * 100;
    
    if (memoryUsage > this.config.memoryThreshold) {
      this.handleMemoryWarning();
    }
  }

  private async checkMemoryUsage() {
    try {
      // In a real implementation, get actual memory usage from native modules
      const memoryUsage = await this.getCurrentMemoryUsage();
      
      performanceMonitor.recordMetric('memory_check', memoryUsage);

      if (memoryUsage > this.config.memoryThreshold) {
        await this.performMemoryCleanup();
      }
    } catch (error) {
      console.error('Failed to check memory usage:', error);
    }
  }

  private async getCurrentMemoryUsage(): Promise<number> {
    // Simulate memory usage - in production, use native modules
    return Math.random() * 100;
  }

  private handleMemoryWarning() {
    console.warn('Memory warning detected - performing cleanup');
    
    // Notify listeners
    this.memoryWarningListeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Memory warning listener error:', error);
      }
    });

    // Perform immediate cleanup
    this.performAggressiveCleanup();
  }

  // Cleanup methods
  async performLightCleanup() {
    try {
      // Clean expired cache items
      await cacheService.optimize();
      
      // Clean old image cache items
      this.cleanupImageCache(0.3); // Remove 30% of oldest items
      
      // Force garbage collection if available
      this.forceGarbageCollection();
      
      performanceMonitor.recordMetric('light_cleanup_performed', 1);
    } catch (error) {
      console.error('Light cleanup failed:', error);
    }
  }

  async performMemoryCleanup() {
    try {
      // Clean cache
      await cacheService.optimize();
      
      // Clean image cache more aggressively
      this.cleanupImageCache(0.5); // Remove 50% of oldest items
      
      // Clear non-essential data
      this.clearNonEssentialData();
      
      // Force garbage collection
      this.forceGarbageCollection();
      
      performanceMonitor.recordMetric('memory_cleanup_performed', 1);
    } catch (error) {
      console.error('Memory cleanup failed:', error);
    }
  }

  async performAggressiveCleanup() {
    try {
      // Clear most cache data
      await cacheService.clear();
      
      // Clear most image cache
      this.cleanupImageCache(0.8); // Remove 80% of items
      
      // Clear all non-essential data
      this.clearNonEssentialData();
      
      // Force multiple garbage collections
      this.forceGarbageCollection();
      setTimeout(() => this.forceGarbageCollection(), 100);
      
      performanceMonitor.recordMetric('aggressive_cleanup_performed', 1);
    } catch (error) {
      console.error('Aggressive cleanup failed:', error);
    }
  }

  private cleanupImageCache(percentage: number) {
    if (!this.config.enableImageOptimization) return;

    const entries = Array.from(this.imageCache.entries());
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
    
    const itemsToRemove = Math.floor(entries.length * percentage);
    
    for (let i = 0; i < itemsToRemove; i++) {
      const [key, value] = entries[i];
      this.totalImageCacheSize -= value.size;
      this.imageCache.delete(key);
    }
  }

  private clearNonEssentialData() {
    // Clear any non-essential data structures
    // This would include things like:
    // - Cached UI components
    // - Temporary data
    // - Analytics data
    // - Non-critical user preferences
  }

  private forceGarbageCollection() {
    // In React Native, we can't directly force GC, but we can help by:
    // 1. Clearing references
    // 2. Setting large objects to null
    // 3. Triggering operations that might cause GC
    
    if (global.gc) {
      global.gc();
    }
  }

  // Image optimization methods
  cacheImage(url: string, imageData: any, size: number) {
    if (!this.config.enableImageOptimization) return;

    // Check if we need to make space
    if (this.totalImageCacheSize + size > this.config.maxImageCacheSize) {
      this.cleanupImageCache(0.3);
    }

    this.imageCache.set(url, {
      data: imageData,
      size,
      lastAccessed: Date.now(),
    });
    
    this.totalImageCacheSize += size;
  }

  getCachedImage(url: string): any | null {
    if (!this.config.enableImageOptimization) return null;

    const cached = this.imageCache.get(url);
    if (cached) {
      cached.lastAccessed = Date.now();
      return cached.data;
    }
    
    return null;
  }

  // Memory monitoring
  addMemoryWarningListener(listener: () => void) {
    this.memoryWarningListeners.push(listener);
    
    return () => {
      const index = this.memoryWarningListeners.indexOf(listener);
      if (index > -1) {
        this.memoryWarningListeners.splice(index, 1);
      }
    };
  }

  // Utility methods for components
  createMemoizedComponent<T>(component: T, dependencies: any[]): T {
    // This would be used with React.memo and useMemo
    // to prevent unnecessary re-renders
    return component;
  }

  optimizeListRendering(data: any[], itemHeight: number, containerHeight: number) {
    // Calculate visible items for virtualization
    const visibleItems = Math.ceil(containerHeight / itemHeight) + 2; // Buffer
    const startIndex = 0; // This would be calculated based on scroll position
    const endIndex = Math.min(startIndex + visibleItems, data.length);
    
    return {
      visibleData: data.slice(startIndex, endIndex),
      startIndex,
      endIndex,
      totalHeight: data.length * itemHeight,
    };
  }

  // Configuration
  updateConfig(newConfig: Partial<MemoryManagerConfig>) {
    this.config = { ...this.config, ...newConfig };
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    if (this.config.enableAutoCleanup) {
      this.startAutoCleanup();
    }
  }

  // Statistics
  getMemoryStats() {
    return {
      imageCacheSize: this.totalImageCacheSize,
      imageCacheItems: this.imageCache.size,
      memoryWarningListeners: this.memoryWarningListeners.length,
      config: this.config,
    };
  }

  // Cleanup
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    
    this.imageCache.clear();
    this.memoryWarningListeners = [];
  }
}

export default new MemoryManager();

// React hooks for memory management
export const useMemoryOptimization = () => {
  const [memoryWarning, setMemoryWarning] = React.useState(false);
  
  React.useEffect(() => {
    const removeListener = memoryManager.addMemoryWarningListener(() => {
      setMemoryWarning(true);
      setTimeout(() => setMemoryWarning(false), 5000); // Clear warning after 5 seconds
    });
    
    return removeListener;
  }, []);
  
  return {
    memoryWarning,
    performCleanup: () => memoryManager.performMemoryCleanup(),
    getStats: () => memoryManager.getMemoryStats(),
  };
};

export const useImageOptimization = () => {
  return {
    cacheImage: (url: string, data: any, size: number) => memoryManager.cacheImage(url, data, size),
    getCachedImage: (url: string) => memoryManager.getCachedImage(url),
  };
};
