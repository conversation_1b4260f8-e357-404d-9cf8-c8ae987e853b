import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { updateNotificationSettings } from '../store/slices/notificationSlice';
import notificationService from '../services/notifications';
import Toast from 'react-native-toast-message';

interface NotificationCategory {
  key: string;
  title: string;
  description: string;
  icon: string;
}

const NotificationManagementScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { settings, isLoading } = useAppSelector(state => state.notification);
  
  const [localSettings, setLocalSettings] = useState(settings);
  const [saving, setSaving] = useState(false);
  const [testingNotification, setTestingNotification] = useState(false);

  const notificationCategories: NotificationCategory[] = [
    {
      key: 'postLikes',
      title: 'Post Likes',
      description: 'When someone likes your posts',
      icon: 'heart',
    },
    {
      key: 'postComments',
      title: 'Post Comments',
      description: 'When someone comments on your posts',
      icon: 'chatbubble',
    },
    {
      key: 'communityUpdates',
      title: 'Community Updates',
      description: 'New posts and activities in your communities',
      icon: 'people',
    },
    {
      key: 'courseUpdates',
      title: 'Course Updates',
      description: 'New lessons and course announcements',
      icon: 'school',
    },
    {
      key: 'paymentUpdates',
      title: 'Payment Updates',
      description: 'Payment confirmations and billing notifications',
      icon: 'card',
    },
    {
      key: 'systemUpdates',
      title: 'System Updates',
      description: 'App updates and important announcements',
      icon: 'settings',
    },
    {
      key: 'messages',
      title: 'Messages',
      description: 'Direct messages and chat notifications',
      icon: 'mail',
    },
  ];

  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      await dispatch(updateNotificationSettings(localSettings)).unwrap();
      
      Toast.show({
        type: 'success',
        text1: 'Settings Saved',
        text2: 'Your notification preferences have been updated',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Save Failed',
        text2: error.message || 'Failed to save notification settings',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTestNotification = async () => {
    try {
      setTestingNotification(true);
      await notificationService.sendTestNotification();
      
      Toast.show({
        type: 'success',
        text1: 'Test Notification Sent',
        text2: 'Check your notification tray',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Test Failed',
        text2: 'Failed to send test notification',
      });
    } finally {
      setTestingNotification(false);
    }
  };

  const handleClearAllNotifications = () => {
    Alert.alert(
      'Clear All Notifications',
      'This will cancel all scheduled notifications. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              await notificationService.cancelAllNotifications();
              await notificationService.clearBadge();
              
              Toast.show({
                type: 'success',
                text1: 'Notifications Cleared',
                text2: 'All scheduled notifications have been cancelled',
              });
            } catch (error) {
              Toast.show({
                type: 'error',
                text1: 'Clear Failed',
                text2: 'Failed to clear notifications',
              });
            }
          },
        },
      ]
    );
  };

  const updateCategorySetting = (category: string, type: 'push' | 'email' | 'inApp', value: boolean) => {
    setLocalSettings(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [category]: value,
      },
    }));
  };

  const updateFrequency = (frequency: 'immediate' | 'hourly' | 'daily' | 'weekly') => {
    setLocalSettings(prev => ({
      ...prev,
      frequency,
    }));
  };

  const updateQuietHours = (field: string, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      quietHours: {
        ...prev.quietHours,
        [field]: value,
      },
    }));
  };

  const renderCategorySettings = (category: NotificationCategory) => (
    <View key={category.key} style={styles.categoryCard}>
      <View style={styles.categoryHeader}>
        <Icon name={category.icon} size={24} color="#007AFF" style={styles.categoryIcon} />
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryTitle}>{category.title}</Text>
          <Text style={styles.categoryDescription}>{category.description}</Text>
        </View>
      </View>

      <View style={styles.categorySettings}>
        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Push</Text>
          <Switch
            value={localSettings.push[category.key as keyof typeof localSettings.push]}
            onValueChange={(value) => updateCategorySetting(category.key, 'push', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Email</Text>
          <Switch
            value={localSettings.email[category.key as keyof typeof localSettings.email]}
            onValueChange={(value) => updateCategorySetting(category.key, 'email', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>In-App</Text>
          <Switch
            value={localSettings.inApp[category.key as keyof typeof localSettings.inApp]}
            onValueChange={(value) => updateCategorySetting(category.key, 'inApp', value)}
            trackColor={{ false: '#ccc', true: '#007AFF' }}
            thumbColor="#fff"
          />
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notification Settings</Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSaveSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <Text style={styles.saveButtonText}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* General Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>General</Text>
          
          {/* Frequency Setting */}
          <View style={styles.settingCard}>
            <Text style={styles.settingTitle}>Notification Frequency</Text>
            <View style={styles.frequencyOptions}>
              {['immediate', 'hourly', 'daily', 'weekly'].map((freq) => (
                <TouchableOpacity
                  key={freq}
                  style={[
                    styles.frequencyOption,
                    localSettings.frequency === freq && styles.frequencyOptionActive
                  ]}
                  onPress={() => updateFrequency(freq as any)}
                >
                  <Text style={[
                    styles.frequencyOptionText,
                    localSettings.frequency === freq && styles.frequencyOptionTextActive
                  ]}>
                    {freq.charAt(0).toUpperCase() + freq.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Quiet Hours */}
          <View style={styles.settingCard}>
            <View style={styles.settingRow}>
              <Text style={styles.settingTitle}>Quiet Hours</Text>
              <Switch
                value={localSettings.quietHours.enabled}
                onValueChange={(value) => updateQuietHours('enabled', value)}
                trackColor={{ false: '#ccc', true: '#007AFF' }}
                thumbColor="#fff"
              />
            </View>
            
            {localSettings.quietHours.enabled && (
              <View style={styles.quietHoursSettings}>
                <Text style={styles.quietHoursText}>
                  No notifications from {localSettings.quietHours.startTime} to {localSettings.quietHours.endTime}
                </Text>
                {/* Time pickers would go here in a real implementation */}
              </View>
            )}
          </View>
        </View>

        {/* Category Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Categories</Text>
          {notificationCategories.map(renderCategorySettings)}
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actions</Text>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleTestNotification}
            disabled={testingNotification}
          >
            {testingNotification ? (
              <ActivityIndicator size="small" color="#007AFF" />
            ) : (
              <Icon name="notifications" size={20} color="#007AFF" />
            )}
            <Text style={styles.actionButtonText}>
              {testingNotification ? 'Sending...' : 'Send Test Notification'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.dangerButton]}
            onPress={handleClearAllNotifications}
          >
            <Icon name="trash" size={20} color="#dc3545" />
            <Text style={[styles.actionButtonText, styles.dangerButtonText]}>
              Clear All Notifications
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  settingCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  settingLabel: {
    fontSize: 14,
    color: '#666',
  },
  frequencyOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  frequencyOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  frequencyOptionActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  frequencyOptionText: {
    fontSize: 14,
    color: '#666',
  },
  frequencyOptionTextActive: {
    color: '#fff',
    fontWeight: '600',
  },
  quietHoursSettings: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  quietHoursText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  categoryCard: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryIcon: {
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
  },
  categorySettings: {
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
    gap: 12,
  },
  dangerButton: {
    borderColor: '#dc3545',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  dangerButtonText: {
    color: '#dc3545',
  },
});

export default NotificationManagementScreen;
