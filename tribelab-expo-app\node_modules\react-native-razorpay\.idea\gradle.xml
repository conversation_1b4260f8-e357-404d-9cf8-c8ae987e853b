<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/android" />
        <option name="gradleHome" value="/usr/local/Cellar/gradle/7.5.1_1/libexec" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/android" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/example/SampleApp/android" />
        <option name="gradleHome" value="/usr/local/Cellar/gradle/7.5.1_1/libexec" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/example/SampleApp/android" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/@react-native-community/cli-platform-android" />
        <option name="gradleHome" value="/usr/local/Cellar/gradle/7.5.1_1/libexec" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/node_modules/@react-native-community/cli-platform-android" />
          </set>
        </option>
      </GradleProjectSettings>
      <GradleProjectSettings>
        <option name="distributionType" value="DEFAULT_WRAPPED" />
        <option name="externalProjectPath" value="$PROJECT_DIR$/node_modules/react-native" />
        <option name="gradleHome" value="/usr/local/Cellar/gradle/7.5.1_1/libexec" />
        <option name="gradleJvm" value="#JAVA_HOME" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$/node_modules/react-native" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>