import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'react-native-chart-kit';

interface AdminWidgetProps {
  type: 'stat' | 'chart' | 'list' | 'progress' | 'alert';
  title: string;
  subtitle?: string;
  value?: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon?: string;
  iconColor?: string;
  chartData?: any;
  chartType?: 'line' | 'bar' | 'pie';
  listData?: Array<{
    id: string;
    title: string;
    subtitle?: string;
    value?: string;
    icon?: string;
  }>;
  progressData?: {
    current: number;
    total: number;
    label: string;
  };
  alertData?: {
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
    action?: {
      label: string;
      onPress: () => void;
    };
  };
  loading?: boolean;
  onPress?: () => void;
  style?: any;
}

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 80; // Account for padding

const AdminWidget: React.FC<AdminWidgetProps> = ({
  type,
  title,
  subtitle,
  value,
  change,
  icon,
  iconColor = '#007AFF',
  chartData,
  chartType = 'line',
  listData = [],
  progressData,
  alertData,
  loading = false,
  onPress,
  style,
}) => {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  const getChangeColor = (changeType: 'increase' | 'decrease') => {
    return changeType === 'increase' ? '#28a745' : '#dc3545';
  };

  const getChangeIcon = (changeType: 'increase' | 'decrease') => {
    return changeType === 'increase' ? 'trending-up' : 'trending-down';
  };

  const getAlertColor = (alertType: string) => {
    switch (alertType) {
      case 'info':
        return '#007AFF';
      case 'warning':
        return '#ffc107';
      case 'error':
        return '#dc3545';
      case 'success':
        return '#28a745';
      default:
        return '#007AFF';
    }
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'info':
        return 'information-circle';
      case 'warning':
        return 'warning';
      case 'error':
        return 'alert-circle';
      case 'success':
        return 'checkmark-circle';
      default:
        return 'information-circle';
    }
  };

  const renderStatWidget = () => (
    <View style={styles.statContent}>
      {icon && (
        <Icon name={icon} size={24} color={iconColor} style={styles.statIcon} />
      )}
      <View style={styles.statInfo}>
        <Text style={styles.statValue}>{formatValue(value || 0)}</Text>
        {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
        {change && (
          <View style={styles.changeContainer}>
            <Icon
              name={getChangeIcon(change.type)}
              size={16}
              color={getChangeColor(change.type)}
            />
            <Text style={[styles.changeText, { color: getChangeColor(change.type) }]}>
              {Math.abs(change.value)}% {change.period}
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderChartWidget = () => {
    if (!chartData) return null;

    const chartConfig = {
      backgroundColor: '#fff',
      backgroundGradientFrom: '#fff',
      backgroundGradientTo: '#fff',
      decimalPlaces: 0,
      color: (opacity = 1) => `rgba(0, 122, 255, ${opacity})`,
      labelColor: (opacity = 1) => `rgba(102, 102, 102, ${opacity})`,
      style: {
        borderRadius: 16,
      },
      propsForDots: {
        r: '4',
        strokeWidth: '2',
        stroke: '#007AFF',
      },
    };

    return (
      <View style={styles.chartContent}>
        {chartType === 'line' && (
          <LineChart
            data={chartData}
            width={chartWidth}
            height={200}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        )}
        {chartType === 'bar' && (
          <BarChart
            data={chartData}
            width={chartWidth}
            height={200}
            chartConfig={chartConfig}
            style={styles.chart}
          />
        )}
        {chartType === 'pie' && (
          <PieChart
            data={chartData}
            width={chartWidth}
            height={200}
            chartConfig={chartConfig}
            accessor="value"
            backgroundColor="transparent"
            paddingLeft="15"
            style={styles.chart}
          />
        )}
      </View>
    );
  };

  const renderListWidget = () => (
    <View style={styles.listContent}>
      {listData.map((item, index) => (
        <View key={item.id} style={[styles.listItem, index < listData.length - 1 && styles.listItemBorder]}>
          {item.icon && (
            <Icon name={item.icon} size={20} color="#666" style={styles.listIcon} />
          )}
          <View style={styles.listItemContent}>
            <Text style={styles.listItemTitle}>{item.title}</Text>
            {item.subtitle && (
              <Text style={styles.listItemSubtitle}>{item.subtitle}</Text>
            )}
          </View>
          {item.value && (
            <Text style={styles.listItemValue}>{item.value}</Text>
          )}
        </View>
      ))}
    </View>
  );

  const renderProgressWidget = () => {
    if (!progressData) return null;

    const percentage = (progressData.current / progressData.total) * 100;

    return (
      <View style={styles.progressContent}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressLabel}>{progressData.label}</Text>
          <Text style={styles.progressValue}>
            {progressData.current} / {progressData.total}
          </Text>
        </View>
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, { width: `${percentage}%` }]} />
        </View>
        <Text style={styles.progressPercentage}>{percentage.toFixed(1)}%</Text>
      </View>
    );
  };

  const renderAlertWidget = () => {
    if (!alertData) return null;

    return (
      <View style={[styles.alertContent, { borderLeftColor: getAlertColor(alertData.type) }]}>
        <Icon
          name={getAlertIcon(alertData.type)}
          size={24}
          color={getAlertColor(alertData.type)}
          style={styles.alertIcon}
        />
        <View style={styles.alertInfo}>
          <Text style={styles.alertMessage}>{alertData.message}</Text>
          {alertData.action && (
            <TouchableOpacity
              style={[styles.alertAction, { backgroundColor: getAlertColor(alertData.type) }]}
              onPress={alertData.action.onPress}
            >
              <Text style={styles.alertActionText}>{alertData.action.label}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
        </View>
      );
    }

    switch (type) {
      case 'stat':
        return renderStatWidget();
      case 'chart':
        return renderChartWidget();
      case 'list':
        return renderListWidget();
      case 'progress':
        return renderProgressWidget();
      case 'alert':
        return renderAlertWidget();
      default:
        return null;
    }
  };

  const Widget = onPress ? TouchableOpacity : View;

  return (
    <Widget
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {onPress && (
          <Icon name="chevron-forward" size={20} color="#666" />
        )}
      </View>
      {renderContent()}
    </Widget>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  loadingContainer: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Stat Widget Styles
  statContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statIcon: {
    marginRight: 12,
  },
  statInfo: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  // Chart Widget Styles
  chartContent: {
    alignItems: 'center',
  },
  chart: {
    borderRadius: 8,
  },
  // List Widget Styles
  listContent: {
    // No additional styles needed
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  listItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listIcon: {
    marginRight: 12,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  listItemSubtitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  listItemValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  // Progress Widget Styles
  progressContent: {
    // No additional styles needed
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  progressValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  // Alert Widget Styles
  alertContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderLeftWidth: 4,
    paddingLeft: 12,
  },
  alertIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  alertInfo: {
    flex: 1,
  },
  alertMessage: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  alertAction: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  alertActionText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
});

export default AdminWidget;
