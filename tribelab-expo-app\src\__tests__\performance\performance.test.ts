import { testHelpers, mockDataGenerators } from '../utils/testUtils';
import performanceMonitor from '../../services/performance';
import cacheService from '../../services/cache';
import memoryManager from '../../utils/memoryManager';

describe('Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    performanceMonitor.clearMetrics();
  });

  describe('Render Performance', () => {
    it('renders large lists efficiently', async () => {
      const largeDataSet = testHelpers.generateArray(
        () => mockDataGenerators.community(),
        1000
      );

      const renderTime = await testHelpers.measureRenderTime(() => {
        // Simulate rendering a large list
        largeDataSet.forEach(item => {
          // Simulate component render work
          JSON.stringify(item);
        });
      });

      expect(renderTime).toBeLessThan(500); // Should render in less than 500ms
      expect(largeDataSet).toHaveLength(1000);
    });

    it('handles rapid state updates efficiently', async () => {
      const startTime = performance.now();
      
      // Simulate rapid state updates
      for (let i = 0; i < 100; i++) {
        await new Promise(resolve => setTimeout(resolve, 1));
        performanceMonitor.recordMetric('state_update', i);
      }
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      expect(totalTime).toBeLessThan(1000); // Should complete in less than 1 second
      
      const metrics = await performanceMonitor.getMetricsSummary();
      expect(metrics.state_update.count).toBe(100);
    });

    it('optimizes image loading performance', async () => {
      const imageUrls = Array.from({ length: 50 }, (_, i) => 
        `https://example.com/image${i}.jpg`
      );

      const startTime = performance.now();
      
      // Simulate image caching
      imageUrls.forEach((url, index) => {
        memoryManager.cacheImage(url, `image-data-${index}`, 1024 * 100); // 100KB each
      });
      
      const endTime = performance.now();
      const cachingTime = endTime - startTime;
      
      expect(cachingTime).toBeLessThan(100); // Should cache quickly
      
      const stats = memoryManager.getMemoryStats();
      expect(stats.imageCacheItems).toBe(50);
    });
  });

  describe('Memory Management', () => {
    it('manages memory usage within limits', async () => {
      const initialMemory = testHelpers.getMemoryUsage();
      
      // Create large data structures
      const largeData = testHelpers.generateArray(
        () => mockDataGenerators.community(),
        500
      );
      
      // Cache the data
      await cacheService.set('test', 'large-data', largeData);
      
      // Trigger memory cleanup
      await memoryManager.performMemoryCleanup();
      
      const finalMemory = testHelpers.getMemoryUsage();
      
      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory.used - initialMemory.used;
        expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB increase
      }
    });

    it('handles memory pressure gracefully', async () => {
      // Simulate memory pressure
      const largeArrays = [];
      
      for (let i = 0; i < 10; i++) {
        largeArrays.push(
          testHelpers.generateArray(
            () => mockDataGenerators.post(),
            100
          )
        );
      }
      
      // Trigger aggressive cleanup
      await memoryManager.performAggressiveCleanup();
      
      // Should not throw errors
      expect(true).toBe(true);
      
      // Clear references
      largeArrays.length = 0;
    });

    it('optimizes cache performance', async () => {
      const cacheKeys = Array.from({ length: 100 }, (_, i) => `key-${i}`);
      const cacheData = cacheKeys.map(key => ({ key, data: mockDataGenerators.user() }));
      
      // Measure cache write performance
      const writeStartTime = performance.now();
      
      for (const { key, data } of cacheData) {
        await cacheService.set('performance-test', key, data);
      }
      
      const writeEndTime = performance.now();
      const writeTime = writeEndTime - writeStartTime;
      
      expect(writeTime).toBeLessThan(1000); // Should write 100 items in less than 1 second
      
      // Measure cache read performance
      const readStartTime = performance.now();
      
      for (const key of cacheKeys) {
        await cacheService.get('performance-test', key);
      }
      
      const readEndTime = performance.now();
      const readTime = readEndTime - readStartTime;
      
      expect(readTime).toBeLessThan(500); // Should read 100 items in less than 500ms
      
      const stats = await cacheService.getStats();
      expect(stats.hits).toBeGreaterThan(0);
    });
  });

  describe('Network Performance', () => {
    it('handles concurrent API requests efficiently', async () => {
      const apiCalls = Array.from({ length: 10 }, (_, i) => 
        fetch(`https://api.example.com/data/${i}`)
      );
      
      const startTime = performance.now();
      
      try {
        await Promise.all(apiCalls);
      } catch (error) {
        // Expected since we're using mock fetch
      }
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      expect(totalTime).toBeLessThan(1000); // Should handle concurrent requests quickly
      expect(global.fetch).toHaveBeenCalledTimes(10);
    });

    it('implements request debouncing', async () => {
      const searchQueries = ['a', 'ab', 'abc', 'abcd', 'abcde'];
      const debouncedRequests: Promise<any>[] = [];
      
      // Simulate rapid search queries with debouncing
      searchQueries.forEach((query, index) => {
        setTimeout(() => {
          const request = fetch(`https://api.example.com/search?q=${query}`);
          debouncedRequests.push(request);
        }, index * 50); // 50ms apart
      });
      
      await testHelpers.waitFor(300); // Wait for all requests
      
      // Should have made fewer requests due to debouncing
      expect(global.fetch).toHaveBeenCalledTimes(1); // Only the last query
    });

    it('caches API responses effectively', async () => {
      const apiUrl = 'https://api.example.com/communities';
      const mockResponse = testHelpers.generateArray(
        () => mockDataGenerators.community(),
        20
      );
      
      // Mock successful API response
      (global.fetch as jest.Mock).mockResolvedValueOnce(
        testHelpers.mockSuccessResponse(mockResponse)
      );
      
      // First request - should hit API
      const startTime1 = performance.now();
      await cacheService.getOrSet('api', apiUrl, async () => {
        const response = await fetch(apiUrl);
        return response.json();
      });
      const endTime1 = performance.now();
      
      // Second request - should hit cache
      const startTime2 = performance.now();
      await cacheService.getOrSet('api', apiUrl, async () => {
        const response = await fetch(apiUrl);
        return response.json();
      });
      const endTime2 = performance.now();
      
      const apiTime = endTime1 - startTime1;
      const cacheTime = endTime2 - startTime2;
      
      expect(cacheTime).toBeLessThan(apiTime); // Cache should be faster
      expect(global.fetch).toHaveBeenCalledTimes(1); // Only one API call
    });
  });

  describe('Animation Performance', () => {
    it('maintains 60fps during animations', async () => {
      const frameCount = 60;
      const frameTimes: number[] = [];
      let lastFrameTime = performance.now();
      
      // Simulate 60 frames of animation
      for (let i = 0; i < frameCount; i++) {
        await new Promise(resolve => {
          requestAnimationFrame(() => {
            const currentTime = performance.now();
            frameTimes.push(currentTime - lastFrameTime);
            lastFrameTime = currentTime;
            resolve(undefined);
          });
        });
      }
      
      const averageFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
      const targetFrameTime = 1000 / 60; // 16.67ms for 60fps
      
      expect(averageFrameTime).toBeLessThan(targetFrameTime * 1.2); // Allow 20% tolerance
    });

    it('handles complex gesture interactions smoothly', async () => {
      const gestureEvents = Array.from({ length: 100 }, (_, i) => ({
        type: 'pan',
        x: i * 2,
        y: Math.sin(i * 0.1) * 50,
        timestamp: performance.now() + i * 16.67, // 60fps
      }));
      
      const startTime = performance.now();
      
      // Simulate processing gesture events
      gestureEvents.forEach(event => {
        performanceMonitor.recordMetric('gesture_event', event.timestamp);
      });
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;
      
      expect(processingTime).toBeLessThan(100); // Should process quickly
      
      const metrics = await performanceMonitor.getMetricsSummary();
      expect(metrics.gesture_event.count).toBe(100);
    });
  });

  describe('Bundle Size and Loading', () => {
    it('keeps bundle size within limits', () => {
      // This would typically be measured by build tools
      // Here we simulate checking component complexity
      const componentCount = 50; // Simulated component count
      const averageComponentSize = 2048; // 2KB average
      const estimatedBundleSize = componentCount * averageComponentSize;
      
      expect(estimatedBundleSize).toBeLessThan(5 * 1024 * 1024); // Less than 5MB
    });

    it('implements code splitting effectively', async () => {
      // Simulate lazy loading of components
      const lazyComponents = [
        () => import('../../screens/CommunityScreen'),
        () => import('../../screens/CourseScreen'),
        () => import('../../screens/ChatScreen'),
      ];
      
      const loadTimes: number[] = [];
      
      for (const loadComponent of lazyComponents) {
        const startTime = performance.now();
        
        try {
          await loadComponent();
        } catch (error) {
          // Expected since we're in test environment
        }
        
        const endTime = performance.now();
        loadTimes.push(endTime - startTime);
      }
      
      const averageLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
      expect(averageLoadTime).toBeLessThan(100); // Should load quickly
    });
  });

  describe('Performance Monitoring', () => {
    it('tracks performance metrics accurately', async () => {
      // Record various metrics
      performanceMonitor.recordMetric('screen_load', 150);
      performanceMonitor.recordMetric('api_call', 300);
      performanceMonitor.recordMetric('cache_hit', 5);
      performanceMonitor.recordNetworkRequest('https://api.example.com/test', 'GET', 250, 1024, 200);
      performanceMonitor.recordRenderTime('TestScreen', 80, 25);
      
      const summary = performanceMonitor.getMetricsSummary();
      const networkSummary = performanceMonitor.getNetworkSummary();
      const renderSummary = performanceMonitor.getRenderSummary();
      
      expect(summary.screen_load.count).toBe(1);
      expect(summary.screen_load.avg).toBe(150);
      expect(networkSummary.totalRequests).toBe(1);
      expect(renderSummary.totalRenders).toBe(1);
    });

    it('detects performance issues', async () => {
      // Record some slow operations
      performanceMonitor.recordMetric('slow_render', 200);
      performanceMonitor.recordMetric('slow_render', 250);
      performanceMonitor.recordNetworkRequest('https://api.example.com/slow', 'GET', 5000, 1024, 200);
      
      const alerts = performanceMonitor.checkPerformanceAlerts();
      
      expect(alerts.length).toBeGreaterThan(0);
      expect(alerts.some(alert => alert.includes('slow'))).toBe(true);
    });
  });
});
