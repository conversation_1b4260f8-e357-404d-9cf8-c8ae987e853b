import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  version: string;
  size: number;
}

interface CacheConfig {
  defaultTTL: number; // Time to live in milliseconds
  maxSize: number; // Maximum cache size in bytes
  version: string; // Cache version for invalidation
  enableCompression: boolean;
  enableEncryption: boolean;
}

class CacheService {
  private config: CacheConfig;
  private memoryCache: Map<string, CacheItem<any>>;
  private cacheStats: {
    hits: number;
    misses: number;
    evictions: number;
    totalSize: number;
  };

  constructor() {
    this.config = {
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      maxSize: 50 * 1024 * 1024, // 50MB
      version: '1.0.0',
      enableCompression: true,
      enableEncryption: false,
    };

    this.memoryCache = new Map();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalSize: 0,
    };

    this.initializeCache();
  }

  private async initializeCache() {
    try {
      // Load cache stats from storage
      const stats = await AsyncStorage.getItem('cache_stats');
      if (stats) {
        this.cacheStats = { ...this.cacheStats, ...JSON.parse(stats) };
      }

      // Clean up expired items on startup
      await this.cleanupExpiredItems();
    } catch (error) {
      console.error('Failed to initialize cache:', error);
    }
  }

  private generateKey(namespace: string, key: string): string {
    return `cache_${namespace}_${key}`;
  }

  private calculateSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 0;
    }
  }

  private async compressData(data: any): Promise<string> {
    if (!this.config.enableCompression) {
      return JSON.stringify(data);
    }
    
    // Simple compression simulation - in production, use a real compression library
    const jsonString = JSON.stringify(data);
    return jsonString;
  }

  private async decompressData(compressedData: string): Promise<any> {
    if (!this.config.enableCompression) {
      return JSON.parse(compressedData);
    }
    
    // Simple decompression simulation
    return JSON.parse(compressedData);
  }

  private async encryptData(data: string): Promise<string> {
    if (!this.config.enableEncryption) {
      return data;
    }
    
    // Simple encryption simulation - in production, use a real encryption library
    return Buffer.from(data).toString('base64');
  }

  private async decryptData(encryptedData: string): Promise<string> {
    if (!this.config.enableEncryption) {
      return encryptedData;
    }
    
    // Simple decryption simulation
    return Buffer.from(encryptedData, 'base64').toString();
  }

  private async evictLRU() {
    const entries = Array.from(this.memoryCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    // Remove oldest 25% of items
    const itemsToRemove = Math.ceil(entries.length * 0.25);
    for (let i = 0; i < itemsToRemove; i++) {
      const [key] = entries[i];
      this.memoryCache.delete(key);
      this.cacheStats.evictions++;
    }
  }

  private async cleanupExpiredItems() {
    const now = Date.now();
    const expiredKeys: string[] = [];

    // Check memory cache
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.expiresAt < now) {
        expiredKeys.push(key);
      }
    }

    // Remove expired items from memory
    expiredKeys.forEach(key => this.memoryCache.delete(key));

    // Check and clean AsyncStorage
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const cacheKeys = allKeys.filter(key => key.startsWith('cache_'));
      
      for (const key of cacheKeys) {
        try {
          const item = await AsyncStorage.getItem(key);
          if (item) {
            const parsedItem: CacheItem<any> = JSON.parse(item);
            if (parsedItem.expiresAt < now || parsedItem.version !== this.config.version) {
              await AsyncStorage.removeItem(key);
            }
          }
        } catch (error) {
          // Remove corrupted items
          await AsyncStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error('Failed to cleanup expired items:', error);
    }
  }

  async set<T>(
    namespace: string,
    key: string,
    data: T,
    ttl?: number,
    options?: {
      persistToDisk?: boolean;
      priority?: 'low' | 'normal' | 'high';
    }
  ): Promise<void> {
    try {
      const cacheKey = this.generateKey(namespace, key);
      const expiresAt = Date.now() + (ttl || this.config.defaultTTL);
      const size = this.calculateSize(data);

      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiresAt,
        version: this.config.version,
        size,
      };

      // Check if we need to evict items
      if (this.cacheStats.totalSize + size > this.config.maxSize) {
        await this.evictLRU();
      }

      // Store in memory cache
      this.memoryCache.set(cacheKey, cacheItem);
      this.cacheStats.totalSize += size;

      // Optionally persist to disk
      if (options?.persistToDisk !== false) {
        const compressedData = await this.compressData(cacheItem);
        const encryptedData = await this.encryptData(compressedData);
        await AsyncStorage.setItem(cacheKey, encryptedData);
      }
    } catch (error) {
      console.error('Failed to set cache item:', error);
    }
  }

  async get<T>(namespace: string, key: string): Promise<T | null> {
    try {
      const cacheKey = this.generateKey(namespace, key);
      const now = Date.now();

      // Check memory cache first
      const memoryItem = this.memoryCache.get(cacheKey);
      if (memoryItem && memoryItem.expiresAt > now) {
        this.cacheStats.hits++;
        // Update timestamp for LRU
        memoryItem.timestamp = now;
        return memoryItem.data;
      }

      // Check disk cache
      const diskItem = await AsyncStorage.getItem(cacheKey);
      if (diskItem) {
        try {
          const decryptedData = await this.decryptData(diskItem);
          const decompressedData = await this.decompressData(decryptedData);
          const cacheItem: CacheItem<T> = decompressedData;

          if (cacheItem.expiresAt > now && cacheItem.version === this.config.version) {
            // Move back to memory cache
            this.memoryCache.set(cacheKey, cacheItem);
            this.cacheStats.hits++;
            return cacheItem.data;
          } else {
            // Remove expired item
            await AsyncStorage.removeItem(cacheKey);
          }
        } catch (error) {
          // Remove corrupted item
          await AsyncStorage.removeItem(cacheKey);
        }
      }

      this.cacheStats.misses++;
      return null;
    } catch (error) {
      console.error('Failed to get cache item:', error);
      this.cacheStats.misses++;
      return null;
    }
  }

  async has(namespace: string, key: string): Promise<boolean> {
    const data = await this.get(namespace, key);
    return data !== null;
  }

  async delete(namespace: string, key: string): Promise<void> {
    try {
      const cacheKey = this.generateKey(namespace, key);
      
      // Remove from memory
      const memoryItem = this.memoryCache.get(cacheKey);
      if (memoryItem) {
        this.cacheStats.totalSize -= memoryItem.size;
        this.memoryCache.delete(cacheKey);
      }

      // Remove from disk
      await AsyncStorage.removeItem(cacheKey);
    } catch (error) {
      console.error('Failed to delete cache item:', error);
    }
  }

  async clear(namespace?: string): Promise<void> {
    try {
      if (namespace) {
        // Clear specific namespace
        const prefix = `cache_${namespace}_`;
        
        // Clear from memory
        for (const key of this.memoryCache.keys()) {
          if (key.startsWith(prefix)) {
            const item = this.memoryCache.get(key);
            if (item) {
              this.cacheStats.totalSize -= item.size;
            }
            this.memoryCache.delete(key);
          }
        }

        // Clear from disk
        const allKeys = await AsyncStorage.getAllKeys();
        const keysToRemove = allKeys.filter(key => key.startsWith(prefix));
        await AsyncStorage.multiRemove(keysToRemove);
      } else {
        // Clear all cache
        this.memoryCache.clear();
        this.cacheStats.totalSize = 0;
        
        const allKeys = await AsyncStorage.getAllKeys();
        const cacheKeys = allKeys.filter(key => key.startsWith('cache_'));
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  async getStats() {
    return {
      ...this.cacheStats,
      memoryItems: this.memoryCache.size,
      hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0,
    };
  }

  async optimize(): Promise<void> {
    try {
      // Clean up expired items
      await this.cleanupExpiredItems();
      
      // Evict items if over size limit
      if (this.cacheStats.totalSize > this.config.maxSize) {
        await this.evictLRU();
      }

      // Save stats
      await AsyncStorage.setItem('cache_stats', JSON.stringify(this.cacheStats));
    } catch (error) {
      console.error('Failed to optimize cache:', error);
    }
  }

  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Utility methods for common cache patterns
  async getOrSet<T>(
    namespace: string,
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    const cached = await this.get<T>(namespace, key);
    if (cached !== null) {
      return cached;
    }

    const data = await fetcher();
    await this.set(namespace, key, data, ttl);
    return data;
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const regex = new RegExp(pattern);
      
      // Invalidate memory cache
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          const item = this.memoryCache.get(key);
          if (item) {
            this.cacheStats.totalSize -= item.size;
          }
          this.memoryCache.delete(key);
        }
      }

      // Invalidate disk cache
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToRemove = allKeys.filter(key => key.startsWith('cache_') && regex.test(key));
      await AsyncStorage.multiRemove(keysToRemove);
    } catch (error) {
      console.error('Failed to invalidate pattern:', error);
    }
  }
}

export default new CacheService();
