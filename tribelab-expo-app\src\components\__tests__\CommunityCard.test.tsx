import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { render, mockDataGenerators } from '../../__tests__/utils/testUtils';
import CommunityCard from '../CommunityCard';

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

describe('CommunityCard', () => {
  const mockCommunity = mockDataGenerators.community({
    name: 'React Native Developers',
    description: 'A community for React Native developers',
    memberCount: 1500,
    category: 'Technology',
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders community information correctly', () => {
      const { getByText, getByTestId } = render(
        <CommunityCard community={mockCommunity} />
      );

      expect(getByText('React Native Developers')).toBeTruthy();
      expect(getByText('A community for React Native developers')).toBeTruthy();
      expect(getByText('1,500 members')).toBeTruthy();
      expect(getByText('Technology')).toBeTruthy();
    });

    it('renders community avatar when provided', () => {
      const { getByTestId } = render(
        <CommunityCard community={mockCommunity} />
      );

      const avatar = getByTestId('community-avatar');
      expect(avatar).toBeTruthy();
      expect(avatar.props.source.uri).toBe(mockCommunity.avatar);
    });

    it('renders placeholder avatar when not provided', () => {
      const communityWithoutAvatar = {
        ...mockCommunity,
        avatar: undefined,
      };

      const { getByTestId } = render(
        <CommunityCard community={communityWithoutAvatar} />
      );

      const avatar = getByTestId('community-avatar');
      expect(avatar.props.source.uri).toContain('placeholder');
    });

    it('shows private badge for private communities', () => {
      const privateCommunity = {
        ...mockCommunity,
        isPrivate: true,
      };

      const { getByText } = render(
        <CommunityCard community={privateCommunity} />
      );

      expect(getByText('Private')).toBeTruthy();
    });

    it('does not show private badge for public communities', () => {
      const { queryByText } = render(
        <CommunityCard community={mockCommunity} />
      );

      expect(queryByText('Private')).toBeNull();
    });
  });

  describe('Interactions', () => {
    it('navigates to community detail when pressed', () => {
      const { getByTestId } = render(
        <CommunityCard community={mockCommunity} />
      );

      const card = getByTestId('community-card');
      fireEvent.press(card);

      expect(mockNavigate).toHaveBeenCalledWith('Community', {
        communityId: mockCommunity.id,
      });
    });

    it('calls onPress callback when provided', () => {
      const mockOnPress = jest.fn();
      const { getByTestId } = render(
        <CommunityCard community={mockCommunity} onPress={mockOnPress} />
      );

      const card = getByTestId('community-card');
      fireEvent.press(card);

      expect(mockOnPress).toHaveBeenCalledWith(mockCommunity);
    });

    it('handles join button press', async () => {
      const mockOnJoin = jest.fn();
      const { getByText } = render(
        <CommunityCard 
          community={mockCommunity} 
          onJoin={mockOnJoin}
          showJoinButton={true}
        />
      );

      const joinButton = getByText('Join');
      fireEvent.press(joinButton);

      await waitFor(() => {
        expect(mockOnJoin).toHaveBeenCalledWith(mockCommunity.id);
      });
    });

    it('shows loading state when joining', async () => {
      const mockOnJoin = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
      const { getByText, getByTestId } = render(
        <CommunityCard 
          community={mockCommunity} 
          onJoin={mockOnJoin}
          showJoinButton={true}
        />
      );

      const joinButton = getByText('Join');
      fireEvent.press(joinButton);

      // Should show loading indicator
      expect(getByTestId('join-loading')).toBeTruthy();

      await waitFor(() => {
        expect(mockOnJoin).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper accessibility labels', () => {
      const { getByTestId } = render(
        <CommunityCard community={mockCommunity} />
      );

      const card = getByTestId('community-card');
      expect(card.props.accessibilityLabel).toBe(
        `Community: ${mockCommunity.name}, ${mockCommunity.memberCount} members`
      );
      expect(card.props.accessibilityRole).toBe('button');
    });

    it('has proper accessibility hint', () => {
      const { getByTestId } = render(
        <CommunityCard community={mockCommunity} />
      );

      const card = getByTestId('community-card');
      expect(card.props.accessibilityHint).toBe('Tap to view community details');
    });
  });

  describe('Edge Cases', () => {
    it('handles very long community names', () => {
      const longNameCommunity = {
        ...mockCommunity,
        name: 'This is a very long community name that should be truncated properly to avoid layout issues',
      };

      const { getByText } = render(
        <CommunityCard community={longNameCommunity} />
      );

      const nameElement = getByText(longNameCommunity.name);
      expect(nameElement.props.numberOfLines).toBe(1);
    });

    it('handles very long descriptions', () => {
      const longDescCommunity = {
        ...mockCommunity,
        description: 'This is a very long description that should be truncated properly to avoid layout issues and maintain good user experience across different screen sizes and devices',
      };

      const { getByText } = render(
        <CommunityCard community={longDescCommunity} />
      );

      const descElement = getByText(longDescCommunity.description);
      expect(descElement.props.numberOfLines).toBe(2);
    });

    it('handles zero member count', () => {
      const emptyCommunity = {
        ...mockCommunity,
        memberCount: 0,
      };

      const { getByText } = render(
        <CommunityCard community={emptyCommunity} />
      );

      expect(getByText('0 members')).toBeTruthy();
    });

    it('handles large member count formatting', () => {
      const largeCommunity = {
        ...mockCommunity,
        memberCount: 1500000,
      };

      const { getByText } = render(
        <CommunityCard community={largeCommunity} />
      );

      expect(getByText('1.5M members')).toBeTruthy();
    });
  });

  describe('Performance', () => {
    it('renders within acceptable time', async () => {
      const renderTime = await testHelpers.measureRenderTime(() => {
        render(<CommunityCard community={mockCommunity} />);
      });

      expect(renderTime).toBeLessThan(100); // Should render in less than 100ms
    });

    it('handles rapid re-renders without memory leaks', () => {
      const { rerender } = render(
        <CommunityCard community={mockCommunity} />
      );

      // Simulate rapid re-renders
      for (let i = 0; i < 10; i++) {
        rerender(
          <CommunityCard 
            community={{
              ...mockCommunity,
              memberCount: mockCommunity.memberCount + i,
            }} 
          />
        );
      }

      // Should not throw or cause memory issues
      expect(true).toBe(true);
    });
  });

  describe('Snapshot Testing', () => {
    it('matches snapshot for default state', () => {
      const { toJSON } = render(
        <CommunityCard community={mockCommunity} />
      );

      expect(toJSON()).toMatchSnapshot();
    });

    it('matches snapshot for private community', () => {
      const privateCommunity = {
        ...mockCommunity,
        isPrivate: true,
      };

      const { toJSON } = render(
        <CommunityCard community={privateCommunity} />
      );

      expect(toJSON()).toMatchSnapshot();
    });

    it('matches snapshot with join button', () => {
      const { toJSON } = render(
        <CommunityCard 
          community={mockCommunity} 
          showJoinButton={true}
        />
      );

      expect(toJSON()).toMatchSnapshot();
    });
  });
});
