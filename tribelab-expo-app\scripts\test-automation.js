#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test automation script for TribeLab mobile app
class TestAutomation {
  constructor() {
    this.testResults = {
      unit: { passed: 0, failed: 0, coverage: 0 },
      integration: { passed: 0, failed: 0, coverage: 0 },
      e2e: { passed: 0, failed: 0, coverage: 0 },
      performance: { passed: 0, failed: 0, coverage: 0 },
    };
    
    this.config = {
      coverageThreshold: 80,
      performanceThreshold: {
        renderTime: 100, // ms
        memoryUsage: 50, // MB
        bundleSize: 5, // MB
      },
      retryCount: 3,
      parallel: true,
    };
  }

  // Main execution method
  async run() {
    console.log('🚀 Starting TribeLab Test Automation Suite\n');
    
    try {
      await this.setupTestEnvironment();
      await this.runLinting();
      await this.runTypeChecking();
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runPerformanceTests();
      await this.runE2ETests();
      await this.generateReports();
      await this.checkQualityGates();
      
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  // Setup test environment
  async setupTestEnvironment() {
    console.log('📋 Setting up test environment...');
    
    // Clear previous test results
    this.clearDirectory('./test-results');
    this.clearDirectory('./coverage');
    
    // Create necessary directories
    this.ensureDirectory('./test-results');
    this.ensureDirectory('./coverage');
    
    // Install dependencies if needed
    if (!fs.existsSync('./node_modules')) {
      console.log('📦 Installing dependencies...');
      execSync('npm install', { stdio: 'inherit' });
    }
    
    console.log('✅ Test environment ready\n');
  }

  // Run ESLint and Prettier
  async runLinting() {
    console.log('🔍 Running code linting...');
    
    try {
      execSync('npx eslint src --ext .ts,.tsx --fix', { stdio: 'pipe' });
      execSync('npx prettier --write "src/**/*.{ts,tsx}"', { stdio: 'pipe' });
      console.log('✅ Linting passed\n');
    } catch (error) {
      console.error('❌ Linting failed');
      throw new Error('Code quality issues found');
    }
  }

  // Run TypeScript type checking
  async runTypeChecking() {
    console.log('🔧 Running TypeScript type checking...');
    
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      console.log('✅ Type checking passed\n');
    } catch (error) {
      console.error('❌ Type checking failed');
      throw new Error('TypeScript errors found');
    }
  }

  // Run unit tests
  async runUnitTests() {
    console.log('🧪 Running unit tests...');
    
    try {
      const result = execSync(
        'npx jest --testMatch="**/*.test.{ts,tsx}" --coverage --coverageDirectory=./coverage/unit --testResultsProcessor=jest-junit',
        { encoding: 'utf8' }
      );
      
      this.parseTestResults(result, 'unit');
      console.log(`✅ Unit tests: ${this.testResults.unit.passed} passed, ${this.testResults.unit.failed} failed\n`);
    } catch (error) {
      this.testResults.unit.failed = this.extractFailedCount(error.stdout);
      console.error(`❌ Unit tests failed: ${this.testResults.unit.failed} failures\n`);
    }
  }

  // Run integration tests
  async runIntegrationTests() {
    console.log('🔗 Running integration tests...');
    
    try {
      const result = execSync(
        'npx jest --testMatch="**/*.integration.test.{ts,tsx}" --coverage --coverageDirectory=./coverage/integration',
        { encoding: 'utf8' }
      );
      
      this.parseTestResults(result, 'integration');
      console.log(`✅ Integration tests: ${this.testResults.integration.passed} passed, ${this.testResults.integration.failed} failed\n`);
    } catch (error) {
      this.testResults.integration.failed = this.extractFailedCount(error.stdout);
      console.error(`❌ Integration tests failed: ${this.testResults.integration.failed} failures\n`);
    }
  }

  // Run performance tests
  async runPerformanceTests() {
    console.log('⚡ Running performance tests...');
    
    try {
      const result = execSync(
        'npx jest --testMatch="**/*.performance.test.{ts,tsx}" --coverage --coverageDirectory=./coverage/performance',
        { encoding: 'utf8' }
      );
      
      this.parseTestResults(result, 'performance');
      console.log(`✅ Performance tests: ${this.testResults.performance.passed} passed, ${this.testResults.performance.failed} failed\n`);
    } catch (error) {
      this.testResults.performance.failed = this.extractFailedCount(error.stdout);
      console.error(`❌ Performance tests failed: ${this.testResults.performance.failed} failures\n`);
    }
  }

  // Run E2E tests
  async runE2ETests() {
    console.log('🎭 Running E2E tests...');
    
    try {
      const result = execSync(
        'npx jest --testMatch="**/*.e2e.test.{ts,tsx}" --coverage --coverageDirectory=./coverage/e2e --testTimeout=30000',
        { encoding: 'utf8' }
      );
      
      this.parseTestResults(result, 'e2e');
      console.log(`✅ E2E tests: ${this.testResults.e2e.passed} passed, ${this.testResults.e2e.failed} failed\n`);
    } catch (error) {
      this.testResults.e2e.failed = this.extractFailedCount(error.stdout);
      console.error(`❌ E2E tests failed: ${this.testResults.e2e.failed} failures\n`);
    }
  }

  // Generate comprehensive test reports
  async generateReports() {
    console.log('📊 Generating test reports...');
    
    // Merge coverage reports
    try {
      execSync('npx nyc merge coverage coverage/merged.json', { stdio: 'pipe' });
      execSync('npx nyc report --reporter=html --reporter=text --reporter=lcov --temp-dir=coverage --report-dir=coverage/final', { stdio: 'pipe' });
    } catch (error) {
      console.warn('⚠️ Coverage merge failed, using individual reports');
    }
    
    // Generate HTML test report
    this.generateHTMLReport();
    
    // Generate JSON summary
    this.generateJSONSummary();
    
    console.log('✅ Reports generated in ./test-results/\n');
  }

  // Check quality gates
  async checkQualityGates() {
    console.log('🚪 Checking quality gates...');
    
    const issues = [];
    
    // Check test coverage
    const totalCoverage = this.calculateOverallCoverage();
    if (totalCoverage < this.config.coverageThreshold) {
      issues.push(`Coverage ${totalCoverage}% below threshold ${this.config.coverageThreshold}%`);
    }
    
    // Check test failures
    const totalFailures = Object.values(this.testResults).reduce((sum, result) => sum + result.failed, 0);
    if (totalFailures > 0) {
      issues.push(`${totalFailures} test failures found`);
    }
    
    // Check performance metrics
    if (this.testResults.performance.failed > 0) {
      issues.push('Performance tests failed');
    }
    
    if (issues.length > 0) {
      console.error('❌ Quality gates failed:');
      issues.forEach(issue => console.error(`  - ${issue}`));
      throw new Error('Quality gates not met');
    }
    
    console.log('✅ All quality gates passed\n');
  }

  // Helper methods
  parseTestResults(output, testType) {
    const passedMatch = output.match(/(\d+) passed/);
    const failedMatch = output.match(/(\d+) failed/);
    const coverageMatch = output.match(/All files\s+\|\s+([\d.]+)/);
    
    this.testResults[testType].passed = passedMatch ? parseInt(passedMatch[1]) : 0;
    this.testResults[testType].failed = failedMatch ? parseInt(failedMatch[1]) : 0;
    this.testResults[testType].coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0;
  }

  extractFailedCount(output) {
    const failedMatch = output.match(/(\d+) failed/);
    return failedMatch ? parseInt(failedMatch[1]) : 1;
  }

  calculateOverallCoverage() {
    const coverages = Object.values(this.testResults).map(result => result.coverage);
    const validCoverages = coverages.filter(coverage => coverage > 0);
    return validCoverages.length > 0 
      ? validCoverages.reduce((sum, coverage) => sum + coverage, 0) / validCoverages.length 
      : 0;
  }

  generateHTMLReport() {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>TribeLab Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #007AFF; color: white; padding: 20px; border-radius: 8px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007AFF; }
        .passed { border-left-color: #28a745; }
        .failed { border-left-color: #dc3545; }
        .metric { font-size: 2em; font-weight: bold; margin-bottom: 10px; }
        .label { color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>TribeLab Mobile App - Test Report</h1>
        <p>Generated on ${new Date().toISOString()}</p>
    </div>
    
    <div class="summary">
        ${Object.entries(this.testResults).map(([type, results]) => `
            <div class="card ${results.failed > 0 ? 'failed' : 'passed'}">
                <div class="metric">${results.passed + results.failed}</div>
                <div class="label">${type.toUpperCase()} TESTS</div>
                <p>Passed: ${results.passed} | Failed: ${results.failed}</p>
                <p>Coverage: ${results.coverage.toFixed(1)}%</p>
            </div>
        `).join('')}
    </div>
    
    <div class="card">
        <h3>Overall Coverage: ${this.calculateOverallCoverage().toFixed(1)}%</h3>
        <p>Threshold: ${this.config.coverageThreshold}%</p>
    </div>
</body>
</html>
    `;
    
    fs.writeFileSync('./test-results/report.html', html);
  }

  generateJSONSummary() {
    const summary = {
      timestamp: new Date().toISOString(),
      results: this.testResults,
      coverage: {
        overall: this.calculateOverallCoverage(),
        threshold: this.config.coverageThreshold,
        passed: this.calculateOverallCoverage() >= this.config.coverageThreshold,
      },
      qualityGates: {
        passed: Object.values(this.testResults).every(result => result.failed === 0) &&
                this.calculateOverallCoverage() >= this.config.coverageThreshold,
      },
    };
    
    fs.writeFileSync('./test-results/summary.json', JSON.stringify(summary, null, 2));
  }

  clearDirectory(dir) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
    }
  }

  ensureDirectory(dir) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }
}

// CLI interface
if (require.main === module) {
  const automation = new TestAutomation();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.slice(2).split('=');
      options[key] = value || true;
    }
  });
  
  // Apply options
  if (options.coverage) {
    automation.config.coverageThreshold = parseInt(options.coverage);
  }
  
  if (options.retry) {
    automation.config.retryCount = parseInt(options.retry);
  }
  
  if (options.sequential) {
    automation.config.parallel = false;
  }
  
  // Run the test suite
  automation.run().catch(error => {
    console.error('Test automation failed:', error);
    process.exit(1);
  });
}

module.exports = TestAutomation;
