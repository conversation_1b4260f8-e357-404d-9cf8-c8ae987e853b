import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { 
  fetchPaymentMethods, 
  addPaymentMethod, 
  processPayment,
  createSubscription 
} from '../store/slices/paymentSlice';
import paymentService from '../services/payment';
import Toast from 'react-native-toast-message';

interface PaymentScreenParams {
  type: 'course' | 'subscription' | 'community';
  itemId: string;
  amount: number;
  currency: string;
  title: string;
  description: string;
}

const PaymentScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  const { paymentMethods, isLoading } = useAppSelector(state => state.payment);
  
  const params = route.params as PaymentScreenParams;
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [showAddCard, setShowAddCard] = useState(false);
  const [cardForm, setCardForm] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    holderName: '',
  });

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      await dispatch(fetchPaymentMethods()).unwrap();
    } catch (error) {
      console.error('Failed to load payment methods:', error);
    }
  };

  const handlePayment = async () => {
    if (!selectedPaymentMethod && paymentMethods.length > 0) {
      Toast.show({
        type: 'error',
        text1: 'Payment Method Required',
        text2: 'Please select a payment method',
      });
      return;
    }

    try {
      setProcessing(true);

      if (params.type === 'subscription') {
        // Handle subscription payment
        const result = await paymentService.createSubscription({
          planId: params.itemId,
          paymentMethodId: selectedPaymentMethod || undefined,
        });

        if (result.success) {
          await dispatch(createSubscription({
            planId: params.itemId,
            paymentMethodId: selectedPaymentMethod || undefined,
          })).unwrap();

          Toast.show({
            type: 'success',
            text1: 'Subscription Created!',
            text2: 'Your subscription is now active',
          });

          navigation.goBack();
        } else {
          throw new Error(result.error);
        }
      } else {
        // Handle one-time payment
        const result = await paymentService.processPayment({
          amount: params.amount,
          currency: params.currency,
          description: params.description,
          metadata: {
            type: params.type,
            itemId: params.itemId,
          },
        });

        if (result.success) {
          await dispatch(processPayment({
            amount: params.amount,
            currency: params.currency,
            type: params.type,
            metadata: {
              itemId: params.itemId,
              paymentId: result.paymentId,
            },
          })).unwrap();

          Toast.show({
            type: 'success',
            text1: 'Payment Successful!',
            text2: 'Your payment has been processed',
          });

          navigation.goBack();
        } else {
          throw new Error(result.error);
        }
      }
    } catch (error: any) {
      console.error('Payment failed:', error);
      Toast.show({
        type: 'error',
        text1: 'Payment Failed',
        text2: error.message || 'Please try again',
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleAddPaymentMethod = async () => {
    if (!cardForm.cardNumber || !cardForm.expiryMonth || !cardForm.expiryYear || !cardForm.cvv) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Card Details',
        text2: 'Please fill in all card details',
      });
      return;
    }

    try {
      const paymentMethodData = {
        type: 'card',
        card: {
          number: cardForm.cardNumber.replace(/\s/g, ''),
          exp_month: parseInt(cardForm.expiryMonth),
          exp_year: parseInt(cardForm.expiryYear),
          cvc: cardForm.cvv,
        },
        billing_details: {
          name: cardForm.holderName,
        },
      };

      await dispatch(addPaymentMethod(paymentMethodData)).unwrap();
      
      Toast.show({
        type: 'success',
        text1: 'Card Added!',
        text2: 'Your payment method has been saved',
      });

      setShowAddCard(false);
      setCardForm({
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: '',
        holderName: '',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Failed to Add Card',
        text2: error.message || 'Please try again',
      });
    }
  };

  const formatCardNumber = (text: string) => {
    const cleaned = text.replace(/\s/g, '');
    const formatted = cleaned.replace(/(.{4})/g, '$1 ').trim();
    return formatted.substring(0, 19); // Limit to 16 digits + 3 spaces
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Order Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.orderItem}>
            <Text style={styles.itemTitle}>{params.title}</Text>
            <Text style={styles.itemDescription}>{params.description}</Text>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>
                {params.type === 'subscription' ? 'Monthly' : 'Total'}
              </Text>
              <Text style={styles.priceAmount}>
                {paymentService.formatCurrency(params.amount, params.currency)}
              </Text>
            </View>
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Payment Method</Text>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddCard(true)}
            >
              <Icon name="add" size={20} color="#007AFF" />
              <Text style={styles.addButtonText}>Add Card</Text>
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
          ) : (
            <>
              {paymentMethods.map((method) => (
                <TouchableOpacity
                  key={method.id}
                  style={[
                    styles.paymentMethod,
                    selectedPaymentMethod === method.id && styles.selectedPaymentMethod,
                  ]}
                  onPress={() => setSelectedPaymentMethod(method.id)}
                >
                  <View style={styles.methodInfo}>
                    <Icon name="card" size={24} color="#333" />
                    <View style={styles.methodDetails}>
                      <Text style={styles.methodTitle}>
                        {method.brand?.toUpperCase()} •••• {method.last4}
                      </Text>
                      <Text style={styles.methodExpiry}>
                        Expires {method.expiryMonth}/{method.expiryYear}
                      </Text>
                    </View>
                  </View>
                  {selectedPaymentMethod === method.id && (
                    <Icon name="checkmark-circle" size={24} color="#007AFF" />
                  )}
                </TouchableOpacity>
              ))}

              {paymentMethods.length === 0 && !showAddCard && (
                <View style={styles.emptyState}>
                  <Icon name="card-outline" size={48} color="#ccc" />
                  <Text style={styles.emptyStateText}>No payment methods added</Text>
                  <TouchableOpacity
                    style={styles.addFirstCardButton}
                    onPress={() => setShowAddCard(true)}
                  >
                    <Text style={styles.addFirstCardButtonText}>Add Your First Card</Text>
                  </TouchableOpacity>
                </View>
              )}
            </>
          )}
        </View>

        {/* Add Card Form */}
        {showAddCard && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Add New Card</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Card Number"
              value={cardForm.cardNumber}
              onChangeText={(text) => setCardForm(prev => ({ 
                ...prev, 
                cardNumber: formatCardNumber(text) 
              }))}
              keyboardType="numeric"
              maxLength={19}
            />

            <View style={styles.row}>
              <TextInput
                style={[styles.input, styles.halfInput]}
                placeholder="MM"
                value={cardForm.expiryMonth}
                onChangeText={(text) => setCardForm(prev => ({ 
                  ...prev, 
                  expiryMonth: text.replace(/\D/g, '').substring(0, 2) 
                }))}
                keyboardType="numeric"
                maxLength={2}
              />
              <TextInput
                style={[styles.input, styles.halfInput]}
                placeholder="YY"
                value={cardForm.expiryYear}
                onChangeText={(text) => setCardForm(prev => ({ 
                  ...prev, 
                  expiryYear: text.replace(/\D/g, '').substring(0, 2) 
                }))}
                keyboardType="numeric"
                maxLength={2}
              />
              <TextInput
                style={[styles.input, styles.halfInput]}
                placeholder="CVV"
                value={cardForm.cvv}
                onChangeText={(text) => setCardForm(prev => ({ 
                  ...prev, 
                  cvv: text.replace(/\D/g, '').substring(0, 4) 
                }))}
                keyboardType="numeric"
                maxLength={4}
                secureTextEntry
              />
            </View>

            <TextInput
              style={styles.input}
              placeholder="Cardholder Name"
              value={cardForm.holderName}
              onChangeText={(text) => setCardForm(prev => ({ 
                ...prev, 
                holderName: text 
              }))}
            />

            <View style={styles.cardFormButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowAddCard(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleAddPaymentMethod}
              >
                <Text style={styles.saveButtonText}>Save Card</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Pay Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.payButton, processing && styles.payButtonDisabled]}
          onPress={handlePayment}
          disabled={processing}
        >
          {processing ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.payButtonText}>
              Pay {paymentService.formatCurrency(params.amount, params.currency)}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginVertical: 10,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#007AFF',
    fontSize: 16,
    marginLeft: 5,
  },
  orderItem: {
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    padding: 15,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 16,
    color: '#333',
  },
  priceAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  loader: {
    marginVertical: 20,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    marginBottom: 10,
  },
  selectedPaymentMethod: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodDetails: {
    marginLeft: 15,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  methodExpiry: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    marginTop: 15,
    marginBottom: 20,
  },
  addFirstCardButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addFirstCardButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    flex: 1,
    marginRight: 10,
  },
  cardFormButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    marginLeft: 10,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    padding: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  payButton: {
    backgroundColor: '#007AFF',
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
  },
  payButtonDisabled: {
    backgroundColor: '#ccc',
  },
  payButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default PaymentScreen;
