import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  FlatList,
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import Icon from "react-native-vector-icons/Ionicons";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import {
  fetchCommunityDetails,
  fetchCommunityPosts,
  setCurrentCommunity,
  clearPosts,
  joinCommunity,
  leaveCommunity,
} from "../store/slices/communitySlice";
import websocketService from "../services/websocket";
import Toast from "react-native-toast-message";
import PostCard from "../components/PostCard";

const CommunityScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  const { currentCommunity, posts, isLoading } = useAppSelector(
    (state) => state.community
  );
  const { user } = useAppSelector((state) => state.auth);

  const [activeTab, setActiveTab] = useState("posts");
  const [refreshing, setRefreshing] = useState(false);
  const [isMember, setIsMember] = useState(false);

  // Get community slug from route params
  const communitySlug =
    (route.params as any)?.slug || "day-by-day-wellness-club";

  useEffect(() => {
    loadCommunityData();

    // Join community room for real-time updates
    if (currentCommunity?.id) {
      websocketService.joinCommunityRoom(currentCommunity.id);
    }

    return () => {
      // Leave community room when component unmounts
      if (currentCommunity?.id) {
        websocketService.leaveCommunityRoom(currentCommunity.id);
      }
    };
  }, [communitySlug]);

  useEffect(() => {
    // Check if user is a member
    if (currentCommunity && user) {
      // This would typically come from the API response
      setIsMember(true); // For now, assume user is a member
    }
  }, [currentCommunity, user]);

  const loadCommunityData = async () => {
    try {
      // Fetch community details
      await dispatch(fetchCommunityDetails(communitySlug)).unwrap();

      // Fetch community posts
      if (currentCommunity?.id) {
        dispatch(clearPosts());
        await dispatch(
          fetchCommunityPosts({
            communityId: currentCommunity.id,
            page: 1,
          })
        ).unwrap();
      }
    } catch (error) {
      console.error("Failed to load community data:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to load community data",
      });
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadCommunityData();
    setRefreshing(false);
  };

  const handleJoinCommunity = async () => {
    if (!currentCommunity) return;

    try {
      await dispatch(
        joinCommunity({
          communityId: currentCommunity.id,
        })
      ).unwrap();

      setIsMember(true);
      Toast.show({
        type: "success",
        text1: "Joined Community!",
        text2: `Welcome to ${currentCommunity.name}`,
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to Join",
        text2: "Please try again",
      });
    }
  };

  const handleLeaveCommunity = async () => {
    if (!currentCommunity) return;

    try {
      await dispatch(leaveCommunity(currentCommunity.id)).unwrap();

      setIsMember(false);
      Toast.show({
        type: "info",
        text1: "Left Community",
        text2: `You have left ${currentCommunity.name}`,
      });
      navigation.goBack();
    } catch (error) {
      Toast.show({
        type: "error",
        text1: "Failed to Leave",
        text2: "Please try again",
      });
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "posts":
        return (
          <FlatList
            data={posts}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => <PostCard post={item} />}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>No posts yet</Text>
                <Text style={styles.emptyStateSubtext}>
                  Be the first to share something!
                </Text>
              </View>
            }
          />
        );
      case "members":
        return (
          <TouchableOpacity
            style={styles.navigationCard}
            onPress={() =>
              navigation.navigate("CommunityMembers", {
                communityId: currentCommunity?.id,
              })
            }
          >
            <Text style={styles.navigationCardTitle}>View All Members</Text>
            <Text style={styles.navigationCardSubtitle}>
              {currentCommunity?.memberCount || 0} members
            </Text>
            <Icon name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        );
      case "events":
        return (
          <TouchableOpacity
            style={styles.navigationCard}
            onPress={() =>
              navigation.navigate("CommunityCalendar", {
                communityId: currentCommunity?.id,
              })
            }
          >
            <Text style={styles.navigationCardTitle}>Community Events</Text>
            <Text style={styles.navigationCardSubtitle}>
              View upcoming events
            </Text>
            <Icon name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        );
      case "about":
        return (
          <View style={styles.aboutContent}>
            <Text style={styles.aboutTitle}>About this community</Text>
            <Text style={styles.aboutDescription}>
              {currentCommunity?.description || "No description available"}
            </Text>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {currentCommunity?.memberCount || 0}
                </Text>
                <Text style={styles.statLabel}>Members</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {currentCommunity?.postCount || 0}
                </Text>
                <Text style={styles.statLabel}>Posts</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {currentCommunity?.createdAt
                    ? new Date(currentCommunity.createdAt).getFullYear()
                    : "2024"}
                </Text>
                <Text style={styles.statLabel}>Created</Text>
              </View>
            </View>
          </View>
        );
      default:
        return null;
    }
  };

  if (isLoading && !currentCommunity) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading community...</Text>
      </View>
    );
  }

  if (!currentCommunity) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Community not found</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={loadCommunityData}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }
  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>
          {currentCommunity?.name || 'Community'}
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('Search')}
          >
            <Icon name="search" size={24} color="#000" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('CommunitySettings', {
              communityId: currentCommunity?.id
            })}
          >
            <Icon name="ellipsis-horizontal" size={24} color="#000" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Community Banner */}
      <View style={styles.bannerContainer}>
        {currentCommunity?.banner && (
          <Image
            source={{ uri: currentCommunity.banner }}
            style={styles.bannerImage}
          />
        )}
        <View style={styles.communityInfo}>
          <View style={styles.avatarContainer}>
            <Image
              source={{
                uri: currentCommunity?.avatar || 'https://via.placeholder.com/60'
              }}
              style={styles.avatar}
            />
          </View>
          <View style={styles.communityDetails}>
            <Text style={styles.communityName}>{currentCommunity?.name}</Text>
            <Text style={styles.memberCount}>
              {currentCommunity?.memberCount || 0} members
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.joinButton,
              isMember ? styles.leaveButton : styles.joinButtonActive
            ]}
            onPress={isMember ? handleLeaveCommunity : handleJoinCommunity}
          >
            <Text style={[
              styles.joinButtonText,
              isMember ? styles.leaveButtonText : styles.joinButtonActiveText
            ]}>
              {isMember ? 'Leave' : 'Join'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Navigation Tabs */}
      <View style={styles.tabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'posts' && styles.activeTab]}
            onPress={() => setActiveTab('posts')}
          >
            <Text style={[styles.tabText, activeTab === 'posts' && styles.activeTabText]}>
              Posts
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'members' && styles.activeTab]}
            onPress={() => setActiveTab('members')}
          >
            <Text style={[styles.tabText, activeTab === 'members' && styles.activeTabText]}>
              Members
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'events' && styles.activeTab]}
            onPress={() => setActiveTab('events')}
          >
            <Text style={[styles.tabText, activeTab === 'events' && styles.activeTabText]}>
              Events
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'about' && styles.activeTab]}
            onPress={() => setActiveTab('about')}
          >
            <Text style={[styles.tabText, activeTab === 'about' && styles.activeTabText]}>
              About
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {renderTabContent()}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingTop: 50,
    paddingBottom: 10,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 10,
  },
  headerActions: {
    flexDirection: "row",
  },
  headerButton: {
    padding: 5,
    marginLeft: 10,
  },
  bannerContainer: {
    backgroundColor: '#f8f9fa',
  },
  bannerImage: {
    width: '100%',
    height: 120,
  },
  communityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  avatarContainer: {
    marginRight: 15,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#e0e0e0',
  },
  communityDetails: {
    flex: 1,
  },
  communityName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  memberCount: {
    fontSize: 14,
    color: '#666',
  },
  joinButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  joinButtonActive: {
    backgroundColor: '#007AFF',
  },
  leaveButton: {
    backgroundColor: 'transparent',
    borderColor: '#ff3b30',
  },
  joinButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  joinButtonActiveText: {
    color: '#fff',
  },
  leaveButtonText: {
    color: '#ff3b30',
  },
  tabs: {
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: '#fff',
  },
  tab: {
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#007AFF",
  },
  tabText: {
    fontSize: 16,
    color: "#666",
  },
  activeTabText: {
    color: "#007AFF",
    fontWeight: "bold",
  },
  tabContent: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  navigationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  navigationCardTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  navigationCardSubtitle: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  aboutContent: {
    padding: 20,
  },
  aboutTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  aboutDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
});

export default CommunityScreen;
