import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchTransactions } from '../store/slices/paymentSlice';
import paymentService from '../services/payment';
import Toast from 'react-native-toast-message';

interface Transaction {
  id: string;
  type: 'subscription' | 'course' | 'community' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  description: string;
  paymentMethod?: string;
  createdAt: string;
  metadata?: any;
  invoiceUrl?: string;
}

const PaymentHistoryScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { transactions, isLoading, pagination } = useAppSelector(state => state.payment);
  
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [filter, setFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      }
      await dispatch(fetchTransactions({ page: 1 })).unwrap();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load payment history',
      });
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreTransactions = async () => {
    if (loadingMore || !pagination.transactions.hasMore) return;
    
    try {
      setLoadingMore(true);
      await dispatch(fetchTransactions({ page: pagination.transactions.page })).unwrap();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load more transactions',
      });
    } finally {
      setLoadingMore(false);
    }
  };

  const handleRefund = (transaction: Transaction) => {
    Alert.alert(
      'Request Refund',
      `Are you sure you want to request a refund for ${paymentService.formatCurrency(transaction.amount, transaction.currency)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request Refund',
          style: 'destructive',
          onPress: () => requestRefund(transaction.id),
        },
      ]
    );
  };

  const requestRefund = async (transactionId: string) => {
    try {
      const success = await paymentService.requestRefund(transactionId, 'User requested refund');
      if (success) {
        Toast.show({
          type: 'success',
          text1: 'Refund Requested',
          text2: 'Your refund request has been submitted',
        });
        loadTransactions(true);
      } else {
        throw new Error('Failed to request refund');
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Refund Failed',
        text2: 'Unable to process refund request',
      });
    }
  };

  const downloadInvoice = async (transaction: Transaction) => {
    try {
      if (transaction.invoiceUrl) {
        // Open invoice URL
        Toast.show({
          type: 'info',
          text1: 'Opening Invoice',
          text2: 'Invoice will open in your browser',
        });
      } else {
        const downloadUrl = await paymentService.downloadInvoice(transaction.id);
        if (downloadUrl) {
          Toast.show({
            type: 'success',
            text1: 'Invoice Ready',
            text2: 'Invoice download started',
          });
        } else {
          throw new Error('Invoice not available');
        }
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Download Failed',
        text2: 'Unable to download invoice',
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#28a745';
      case 'pending':
        return '#ffc107';
      case 'failed':
        return '#dc3545';
      case 'refunded':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'pending':
        return 'time';
      case 'failed':
        return 'close-circle';
      case 'refunded':
        return 'return-up-back';
      default:
        return 'help-circle';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'subscription':
        return 'card';
      case 'course':
        return 'school';
      case 'community':
        return 'people';
      case 'refund':
        return 'return-up-back';
      default:
        return 'receipt';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'all') return true;
    return transaction.status === filter;
  });

  const renderTransaction = ({ item: transaction }: { item: Transaction }) => (
    <View style={styles.transactionCard}>
      <View style={styles.transactionHeader}>
        <View style={styles.transactionInfo}>
          <View style={styles.transactionTitleRow}>
            <Icon 
              name={getTypeIcon(transaction.type)} 
              size={20} 
              color="#007AFF" 
              style={styles.typeIcon}
            />
            <Text style={styles.transactionDescription}>
              {transaction.description}
            </Text>
          </View>
          <Text style={styles.transactionDate}>
            {formatDate(transaction.createdAt)}
          </Text>
          {transaction.paymentMethod && (
            <Text style={styles.paymentMethod}>
              via {transaction.paymentMethod}
            </Text>
          )}
        </View>
        
        <View style={styles.transactionAmount}>
          <Text style={[
            styles.amountText,
            { color: transaction.type === 'refund' ? '#dc3545' : '#333' }
          ]}>
            {transaction.type === 'refund' ? '-' : ''}
            {paymentService.formatCurrency(transaction.amount, transaction.currency)}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(transaction.status) }]}>
            <Icon name={getStatusIcon(transaction.status)} size={12} color="#fff" />
            <Text style={styles.statusText}>
              {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
            </Text>
          </View>
        </View>
      </View>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        {transaction.status === 'completed' && transaction.type !== 'refund' && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => downloadInvoice(transaction)}
          >
            <Icon name="download" size={16} color="#007AFF" />
            <Text style={styles.actionButtonText}>Invoice</Text>
          </TouchableOpacity>
        )}
        
        {transaction.status === 'completed' && 
         transaction.type !== 'refund' && 
         new Date(transaction.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) && (
          <TouchableOpacity
            style={[styles.actionButton, styles.refundButton]}
            onPress={() => handleRefund(transaction)}
          >
            <Icon name="return-up-back" size={16} color="#dc3545" />
            <Text style={[styles.actionButtonText, styles.refundButtonText]}>Refund</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment History</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Filter tabs */}
      <View style={styles.filterTabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {['all', 'completed', 'pending', 'failed'].map((filterOption) => (
            <TouchableOpacity
              key={filterOption}
              style={[
                styles.filterTab,
                filter === filterOption && styles.activeFilterTab
              ]}
              onPress={() => setFilter(filterOption as any)}
            >
              <Text style={[
                styles.filterTabText,
                filter === filterOption && styles.activeFilterTabText
              ]}>
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Transactions list */}
      {isLoading && transactions.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading payment history...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          style={styles.transactionsList}
          contentContainerStyle={styles.transactionsContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={() => loadTransactions(true)} />
          }
          onEndReached={loadMoreTransactions}
          onEndReachedThreshold={0.1}
          ListFooterComponent={
            loadingMore ? (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color="#007AFF" />
              </View>
            ) : null
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Icon name="receipt-outline" size={64} color="#ccc" />
              <Text style={styles.emptyTitle}>No Transactions</Text>
              <Text style={styles.emptyText}>
                Your payment history will appear here
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  filterTabs: {
    backgroundColor: '#fff',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 5,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
  },
  activeFilterTab: {
    backgroundColor: '#007AFF',
  },
  filterTabText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  transactionsList: {
    flex: 1,
  },
  transactionsContent: {
    padding: 20,
  },
  transactionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  transactionInfo: {
    flex: 1,
    marginRight: 12,
  },
  transactionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  typeIcon: {
    marginRight: 8,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  transactionDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  paymentMethod: {
    fontSize: 12,
    color: '#999',
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    gap: 4,
  },
  refundButton: {
    borderColor: '#dc3545',
  },
  actionButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  refundButtonText: {
    color: '#dc3545',
  },
  loadMoreContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default PaymentHistoryScreen;
