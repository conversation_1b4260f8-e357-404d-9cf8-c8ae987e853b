* **Common to Both platforms :-**

  - [ ] I have gone through the README (https://github.com/razorpay/react-native-razorpay)
  - [ ] I have searched for a similar issue (https://github.com/razorpay/react-native-razorpay/issues)
  - [ ] I am using the latest version of our framework (https://github.com/razorpay/react-native-razorpay/releases)
  - [ ] I have cleaned my project.
  - [ ] I have gone through FAQ's (https://github.com/razorpay/react-native-razorpay/wiki/FAQ's)


* **Specific to iOS Users :-**

  - [ ] I have modified the module map as specified in the readme (https://github.com/razorpay/react-native-razorpay)
  - [ ] I have deleted the contents of the derived data folder and rebuild it.

<!-- Describe your issue in detail. -->

## IDE Specs
<!-- Required. Specify your Xcode Version (iOS) -->
<!-- Required. Specify your Java and Gradle version (Android) -->
<!-- Razorpay Package Version -->

## Retro Steps
<!-- 
  Required.
-->

## Screenshots 
<!-- Optional.It'll just help us understand your issue better. -->
