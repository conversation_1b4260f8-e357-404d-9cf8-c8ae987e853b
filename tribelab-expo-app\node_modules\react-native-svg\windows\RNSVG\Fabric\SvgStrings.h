#pragma once

namespace winrt::RNSVG::SvgStrings {
	constexpr wchar_t fillAttributeName[] = L"fill";
	constexpr wchar_t clipPathAttributeName[] = L"clip-path";
	constexpr wchar_t clipRuleAttributeName[] = L"clip-rule";
	constexpr wchar_t fillRuleAttributeName[] = L"fill-rule";

	constexpr wchar_t colorAttributeName[] = L"color";
	constexpr wchar_t opacityAttributeName[] = L"opacity";
	constexpr wchar_t fillOpacityAttributeName[] = L"fill-opacity";
	constexpr wchar_t stopOpacityAttributeName[] = L"stop-opacity";
	constexpr wchar_t strokeAttributeName[] = L"stroke";
	constexpr wchar_t strokeWidthAttributeName[] = L"stroke-width";
	constexpr wchar_t strokeOpacityAttributeName[] = L"stroke-opacity";
	constexpr wchar_t strokeLinecapAttributeName[] = L"stroke-linecap";
	constexpr wchar_t strokeLinejoinAttributeName[] = L"stroke-linejoin";
	constexpr wchar_t strokeDashArrayAttributeName[] = L"stroke-dasharray";
	constexpr wchar_t strokeDashOffsetAttributeName[] = L"stroke-dashoffset";
	constexpr wchar_t strokeMiterLimitAttributeName[] = L"stroke-miterlimit";
	constexpr wchar_t idAttributeName[] = L"id";
	constexpr wchar_t transformAttributeName[] = L"transform";

	constexpr wchar_t xAttributeName[] = L"x";
	constexpr wchar_t yAttributeName[] = L"y";
	constexpr wchar_t widthAttributeName[] = L"width";
	constexpr wchar_t heightAttributeName[] = L"height";

	constexpr wchar_t cxAttributeName[] = L"cx";
	constexpr wchar_t cyAttributeName[] = L"cy";
	constexpr wchar_t rxAttributeName[] = L"rx";
	constexpr wchar_t ryAttributeName[] = L"ry";

	constexpr wchar_t rAttributeName[] = L"r";
	constexpr wchar_t fxAttributeName[] = L"fx";
	constexpr wchar_t fyAttributeName[] = L"fy";

	constexpr wchar_t x1AttributeName[] = L"x1";
	constexpr wchar_t y1AttributeName[] = L"y1";

	constexpr wchar_t x2AttributeName[] = L"x2";
	constexpr wchar_t y2AttributeName[] = L"y2";

	constexpr wchar_t gradientUnitsAttributeName[] = L"gradientUnits";
	constexpr wchar_t gradientTransformAttributeName[] = L"gradientTransform";
	constexpr wchar_t offsetAttributeName[] = L"offset";
	constexpr wchar_t stopColorAttributeName[] = L"stop-color";
	constexpr wchar_t stopAttributeName[] = L"stop";

	constexpr wchar_t xlinkhrefAttributeName[] = L"xlink:href";

	constexpr wchar_t dAttributeName[] = L"d";

	constexpr wchar_t noneAttributeValue[] = L"none";
	
	constexpr wchar_t viewBoxAttributeName[] = L"viewBox";
	constexpr wchar_t preserveAspectRatioAttributeName[] = L"preserveAspectRatio";
}