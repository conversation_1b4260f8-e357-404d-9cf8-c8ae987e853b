import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { searchContent, addRecentSearch } from '../store/slices/searchSlice';

interface Recommendation {
  id: string;
  type: 'trending' | 'suggested' | 'recent' | 'popular';
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: string;
  metadata?: {
    category?: string;
    memberCount?: number;
    searchCount?: number;
    relevanceScore?: number;
  };
}

interface SearchRecommendationsProps {
  onRecommendationPress: (recommendation: Recommendation) => void;
  onSearchPress: (query: string) => void;
  style?: any;
}

const SearchRecommendations: React.FC<SearchRecommendationsProps> = ({
  onRecommendationPress,
  onSearchPress,
  style,
}) => {
  const dispatch = useAppDispatch();
  const { trendingSearches, recentSearches } = useAppSelector(state => state.search);
  
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [activeTab, setActiveTab] = useState<'trending' | 'suggested' | 'recent'>('trending');

  useEffect(() => {
    loadRecommendations();
  }, [activeTab]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      
      // Mock recommendations data - replace with real API calls
      const mockRecommendations: Recommendation[] = [];
      
      if (activeTab === 'trending') {
        mockRecommendations.push(
          {
            id: '1',
            type: 'trending',
            title: 'React Native Development',
            subtitle: 'Technology',
            description: 'Learn mobile app development with React Native',
            metadata: { searchCount: 1250, category: 'Technology' },
          },
          {
            id: '2',
            type: 'trending',
            title: 'UI/UX Design Community',
            subtitle: 'Design Community',
            description: 'Connect with designers and share your work',
            avatar: 'https://via.placeholder.com/50',
            metadata: { memberCount: 8500, category: 'Design' },
          },
          {
            id: '3',
            type: 'trending',
            title: 'JavaScript Fundamentals',
            subtitle: 'Programming Course',
            description: 'Master the basics of JavaScript programming',
            metadata: { searchCount: 980, category: 'Technology' },
          },
          {
            id: '4',
            type: 'trending',
            title: 'Digital Marketing Tips',
            subtitle: 'Business',
            description: 'Grow your business with digital marketing strategies',
            metadata: { searchCount: 750, category: 'Business' },
          },
        );
      } else if (activeTab === 'suggested') {
        mockRecommendations.push(
          {
            id: '5',
            type: 'suggested',
            title: 'Mobile App Design',
            subtitle: 'Based on your interests',
            description: 'Design beautiful mobile applications',
            metadata: { relevanceScore: 0.95, category: 'Design' },
          },
          {
            id: '6',
            type: 'suggested',
            title: 'Startup Founders Network',
            subtitle: 'Entrepreneurship Community',
            description: 'Connect with fellow entrepreneurs and founders',
            avatar: 'https://via.placeholder.com/50',
            metadata: { memberCount: 3200, category: 'Business' },
          },
          {
            id: '7',
            type: 'suggested',
            title: 'Fitness Motivation',
            subtitle: 'Health & Wellness',
            description: 'Stay motivated on your fitness journey',
            metadata: { relevanceScore: 0.88, category: 'Health' },
          },
        );
      } else if (activeTab === 'recent') {
        // Convert recent searches to recommendations
        recentSearches.forEach((search, index) => {
          mockRecommendations.push({
            id: `recent_${index}`,
            type: 'recent',
            title: search,
            subtitle: 'Recent search',
            description: `You searched for "${search}" recently`,
          });
        });
      }
      
      setRecommendations(mockRecommendations);
    } catch (error) {
      console.error('Failed to load recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRecommendationPress = (recommendation: Recommendation) => {
    // Add to recent searches if it's a search query
    if (recommendation.type !== 'recent') {
      dispatch(addRecentSearch(recommendation.title));
    }
    
    onRecommendationPress(recommendation);
  };

  const handleSearchPress = (query: string) => {
    dispatch(addRecentSearch(query));
    onSearchPress(query);
  };

  const getRecommendationIcon = (type: string, category?: string) => {
    if (type === 'recent') return 'time';
    if (type === 'trending') return 'trending-up';
    
    switch (category?.toLowerCase()) {
      case 'technology':
        return 'code-slash';
      case 'design':
        return 'color-palette';
      case 'business':
        return 'briefcase';
      case 'health':
        return 'fitness';
      default:
        return 'search';
    }
  };

  const getRecommendationColor = (type: string) => {
    switch (type) {
      case 'trending':
        return '#FF6B6B';
      case 'suggested':
        return '#4ECDC4';
      case 'recent':
        return '#95A5A6';
      default:
        return '#007AFF';
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {['trending', 'suggested', 'recent'].map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[
            styles.tab,
            activeTab === tab && styles.activeTab
          ]}
          onPress={() => setActiveTab(tab as any)}
        >
          <Text style={[
            styles.tabText,
            activeTab === tab && styles.activeTabText
          ]}>
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderRecommendation = ({ item: recommendation }: { item: Recommendation }) => (
    <TouchableOpacity
      style={styles.recommendationCard}
      onPress={() => handleRecommendationPress(recommendation)}
      activeOpacity={0.7}
    >
      <View style={styles.recommendationContent}>
        <View style={styles.recommendationHeader}>
          <View style={[
            styles.iconContainer,
            { backgroundColor: getRecommendationColor(recommendation.type) }
          ]}>
            <Icon
              name={getRecommendationIcon(recommendation.type, recommendation.metadata?.category)}
              size={20}
              color="#fff"
            />
          </View>
          
          {recommendation.avatar && (
            <Image
              source={{ uri: recommendation.avatar }}
              style={styles.recommendationAvatar}
            />
          )}
        </View>
        
        <View style={styles.recommendationInfo}>
          <Text style={styles.recommendationTitle} numberOfLines={1}>
            {recommendation.title}
          </Text>
          
          {recommendation.subtitle && (
            <Text style={styles.recommendationSubtitle} numberOfLines={1}>
              {recommendation.subtitle}
            </Text>
          )}
          
          {recommendation.description && (
            <Text style={styles.recommendationDescription} numberOfLines={2}>
              {recommendation.description}
            </Text>
          )}
          
          <View style={styles.recommendationMeta}>
            {recommendation.metadata?.searchCount && (
              <Text style={styles.metaText}>
                {recommendation.metadata.searchCount} searches
              </Text>
            )}
            
            {recommendation.metadata?.memberCount && (
              <Text style={styles.metaText}>
                {recommendation.metadata.memberCount.toLocaleString()} members
              </Text>
            )}
            
            {recommendation.metadata?.relevanceScore && (
              <Text style={styles.metaText}>
                {Math.round(recommendation.metadata.relevanceScore * 100)}% match
              </Text>
            )}
          </View>
        </View>
      </View>
      
      <Icon name="chevron-forward" size={20} color="#ccc" />
    </TouchableOpacity>
  );

  const renderQuickSearches = () => {
    const quickSearches = [
      'React Native',
      'UI Design',
      'JavaScript',
      'Startup',
      'Fitness',
      'Marketing',
    ];

    return (
      <View style={styles.quickSearchesContainer}>
        <Text style={styles.quickSearchesTitle}>Quick Searches</Text>
        <View style={styles.quickSearchesGrid}>
          {quickSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={styles.quickSearchChip}
              onPress={() => handleSearchPress(search)}
            >
              <Icon name="search" size={16} color="#666" />
              <Text style={styles.quickSearchText}>{search}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {renderTabBar()}
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
        </View>
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {activeTab === 'trending' && renderQuickSearches()}
          
          <FlatList
            data={recommendations}
            renderItem={renderRecommendation}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Icon name="search-outline" size={48} color="#ccc" />
                <Text style={styles.emptyTitle}>No Recommendations</Text>
                <Text style={styles.emptyText}>
                  {activeTab === 'recent' 
                    ? 'Your recent searches will appear here'
                    : 'Check back later for personalized recommendations'
                  }
                </Text>
              </View>
            }
          />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  quickSearchesContainer: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  quickSearchesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  quickSearchesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickSearchChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 6,
  },
  quickSearchText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  recommendationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recommendationAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  recommendationInfo: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  recommendationSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  recommendationDescription: {
    fontSize: 14,
    color: '#999',
    lineHeight: 18,
    marginBottom: 6,
  },
  recommendationMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  metaText: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default SearchRecommendations;
