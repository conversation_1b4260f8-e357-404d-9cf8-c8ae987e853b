import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { 
  fetchPaymentMethods, 
  addPaymentMethod, 
  removePaymentMethod,
  setDefaultPaymentMethod 
} from '../store/slices/paymentSlice';
import Toast from 'react-native-toast-message';

interface PaymentMethod {
  id: string;
  type: 'card' | 'upi' | 'netbanking' | 'wallet';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  createdAt: string;
}

const PaymentMethodsScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { paymentMethods, isLoading } = useAppSelector(state => state.payment);
  
  const [showAddModal, setShowAddModal] = useState(false);
  const [addingMethod, setAddingMethod] = useState(false);
  const [removingMethod, setRemovingMethod] = useState<string | null>(null);
  const [newCardForm, setNewCardForm] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    holderName: '',
  });

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      await dispatch(fetchPaymentMethods()).unwrap();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load payment methods',
      });
    }
  };

  const handleAddPaymentMethod = async () => {
    try {
      setAddingMethod(true);
      
      // Validate form
      if (!newCardForm.cardNumber || !newCardForm.expiryMonth || 
          !newCardForm.expiryYear || !newCardForm.cvv || !newCardForm.holderName) {
        Toast.show({
          type: 'error',
          text1: 'Validation Error',
          text2: 'Please fill all required fields',
        });
        return;
      }

      // Format card number (remove spaces)
      const cardNumber = newCardForm.cardNumber.replace(/\s/g, '');
      
      if (cardNumber.length < 13 || cardNumber.length > 19) {
        Toast.show({
          type: 'error',
          text1: 'Invalid Card',
          text2: 'Please enter a valid card number',
        });
        return;
      }

      await dispatch(addPaymentMethod({
        type: 'card',
        cardNumber,
        expiryMonth: parseInt(newCardForm.expiryMonth),
        expiryYear: parseInt(newCardForm.expiryYear),
        cvv: newCardForm.cvv,
        holderName: newCardForm.holderName,
      })).unwrap();

      Toast.show({
        type: 'success',
        text1: 'Payment Method Added',
        text2: 'Your card has been added successfully',
      });

      setShowAddModal(false);
      setNewCardForm({
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: '',
        holderName: '',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Failed to Add Card',
        text2: error.message || 'Please try again',
      });
    } finally {
      setAddingMethod(false);
    }
  };

  const handleRemovePaymentMethod = (paymentMethod: PaymentMethod) => {
    if (paymentMethod.isDefault) {
      Alert.alert(
        'Cannot Remove Default',
        'You cannot remove your default payment method. Please set another method as default first.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Remove Payment Method',
      `Are you sure you want to remove this ${paymentMethod.type}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => confirmRemovePaymentMethod(paymentMethod.id),
        },
      ]
    );
  };

  const confirmRemovePaymentMethod = async (paymentMethodId: string) => {
    try {
      setRemovingMethod(paymentMethodId);
      await dispatch(removePaymentMethod(paymentMethodId)).unwrap();
      
      Toast.show({
        type: 'success',
        text1: 'Payment Method Removed',
        text2: 'The payment method has been removed',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Removal Failed',
        text2: error.message || 'Failed to remove payment method',
      });
    } finally {
      setRemovingMethod(null);
    }
  };

  const handleSetDefault = async (paymentMethodId: string) => {
    try {
      dispatch(setDefaultPaymentMethod(paymentMethodId));
      Toast.show({
        type: 'success',
        text1: 'Default Updated',
        text2: 'Default payment method has been updated',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Update Failed',
        text2: 'Failed to update default payment method',
      });
    }
  };

  const getCardBrandIcon = (brand?: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return 'card';
      case 'mastercard':
        return 'card';
      case 'amex':
        return 'card';
      default:
        return 'card';
    }
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return 'card';
      case 'upi':
        return 'phone-portrait';
      case 'netbanking':
        return 'business';
      case 'wallet':
        return 'wallet';
      default:
        return 'card';
    }
  };

  const formatCardNumber = (value: string) => {
    // Remove all non-digit characters
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <View key={method.id} style={styles.paymentMethodCard}>
      <View style={styles.methodHeader}>
        <View style={styles.methodInfo}>
          <Icon 
            name={getPaymentMethodIcon(method.type)} 
            size={24} 
            color="#007AFF" 
            style={styles.methodIcon}
          />
          <View style={styles.methodDetails}>
            <Text style={styles.methodType}>
              {method.type.charAt(0).toUpperCase() + method.type.slice(1)}
              {method.brand && ` (${method.brand})`}
            </Text>
            {method.last4 && (
              <Text style={styles.methodNumber}>
                •••• •••• •••• {method.last4}
              </Text>
            )}
            {method.expiryMonth && method.expiryYear && (
              <Text style={styles.methodExpiry}>
                Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear}
              </Text>
            )}
          </View>
        </View>
        
        {method.isDefault && (
          <View style={styles.defaultBadge}>
            <Text style={styles.defaultText}>Default</Text>
          </View>
        )}
      </View>

      <View style={styles.methodActions}>
        {!method.isDefault && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleSetDefault(method.id)}
          >
            <Text style={styles.actionButtonText}>Set as Default</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.removeButton]}
          onPress={() => handleRemovePaymentMethod(method)}
          disabled={removingMethod === method.id}
        >
          {removingMethod === method.id ? (
            <ActivityIndicator size="small" color="#dc3545" />
          ) : (
            <Text style={[styles.actionButtonText, styles.removeButtonText]}>Remove</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Methods</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Icon name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>Loading payment methods...</Text>
          </View>
        ) : paymentMethods.length > 0 ? (
          <View style={styles.methodsList}>
            {paymentMethods.map(renderPaymentMethod)}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="card-outline" size={64} color="#ccc" />
            <Text style={styles.emptyTitle}>No Payment Methods</Text>
            <Text style={styles.emptyText}>
              Add a payment method to make purchases
            </Text>
            <TouchableOpacity
              style={styles.addFirstButton}
              onPress={() => setShowAddModal(true)}
            >
              <Icon name="add" size={20} color="#fff" />
              <Text style={styles.addFirstButtonText}>Add Payment Method</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Add Payment Method Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowAddModal(false)}
            >
              <Icon name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Payment Method</Text>
            <View style={styles.placeholder} />
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Card Number</Text>
              <TextInput
                style={styles.formInput}
                value={newCardForm.cardNumber}
                onChangeText={(text) => setNewCardForm(prev => ({ 
                  ...prev, 
                  cardNumber: formatCardNumber(text) 
                }))}
                placeholder="1234 5678 9012 3456"
                keyboardType="numeric"
                maxLength={19}
              />
            </View>

            <View style={styles.formRow}>
              <View style={[styles.formGroup, styles.formGroupHalf]}>
                <Text style={styles.formLabel}>Expiry Month</Text>
                <TextInput
                  style={styles.formInput}
                  value={newCardForm.expiryMonth}
                  onChangeText={(text) => setNewCardForm(prev => ({ 
                    ...prev, 
                    expiryMonth: text.replace(/[^0-9]/g, '') 
                  }))}
                  placeholder="MM"
                  keyboardType="numeric"
                  maxLength={2}
                />
              </View>

              <View style={[styles.formGroup, styles.formGroupHalf]}>
                <Text style={styles.formLabel}>Expiry Year</Text>
                <TextInput
                  style={styles.formInput}
                  value={newCardForm.expiryYear}
                  onChangeText={(text) => setNewCardForm(prev => ({ 
                    ...prev, 
                    expiryYear: text.replace(/[^0-9]/g, '') 
                  }))}
                  placeholder="YYYY"
                  keyboardType="numeric"
                  maxLength={4}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>CVV</Text>
              <TextInput
                style={styles.formInput}
                value={newCardForm.cvv}
                onChangeText={(text) => setNewCardForm(prev => ({ 
                  ...prev, 
                  cvv: text.replace(/[^0-9]/g, '') 
                }))}
                placeholder="123"
                keyboardType="numeric"
                maxLength={4}
                secureTextEntry
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Cardholder Name</Text>
              <TextInput
                style={styles.formInput}
                value={newCardForm.holderName}
                onChangeText={(text) => setNewCardForm(prev => ({ 
                  ...prev, 
                  holderName: text 
                }))}
                placeholder="John Doe"
                autoCapitalize="words"
              />
            </View>

            <TouchableOpacity
              style={[styles.addCardButton, addingMethod && styles.addCardButtonDisabled]}
              onPress={handleAddPaymentMethod}
              disabled={addingMethod}
            >
              {addingMethod ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.addCardButtonText}>Add Card</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    padding: 5,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  methodsList: {
    padding: 20,
  },
  paymentMethodCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodIcon: {
    marginRight: 12,
  },
  methodDetails: {
    flex: 1,
  },
  methodType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  methodNumber: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  methodExpiry: {
    fontSize: 12,
    color: '#999',
  },
  defaultBadge: {
    backgroundColor: '#28a745',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  defaultText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  methodActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    alignItems: 'center',
  },
  removeButton: {
    borderColor: '#dc3545',
  },
  actionButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  removeButtonText: {
    color: '#dc3545',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  addFirstButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  addFirstButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  formGroupHalf: {
    flex: 1,
  },
  formRow: {
    flexDirection: 'row',
    gap: 12,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  addCardButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  addCardButtonDisabled: {
    backgroundColor: '#ccc',
  },
  addCardButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PaymentMethodsScreen;
