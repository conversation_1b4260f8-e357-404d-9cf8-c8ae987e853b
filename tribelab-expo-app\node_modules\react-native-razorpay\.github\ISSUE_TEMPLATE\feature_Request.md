---
name: 💡 Feature Request
about: I have a suggestion (and might want to implement myself 🙂)!
---

<!--

Vote on feature requests by adding a 👍. This helps maintainers prioritize what
to work on.

* Please fill out this template with all the relevant information so we can
  understand what's going on and fix the issue. We appreciate bugs filed and PRs
  submitted!

* Please make sure that you are familiar with and follow the Code of Conduct for
  this project (found in the CODE_OF_CONDUCT.md file).

-->

### Describe the feature you'd like:

<!--
A clear and concise description of what you want to happen. Add any considered
drawbacks.
-->

### Suggested implementation:

<!-- Helpful but optional 😀 -->

### Describe alternatives you've considered:

<!--
A clear and concise description of any alternative solutions or features you've
considered.
-->

### Teachability, Documentation, Adoption, Migration Strategy:

<!--
If you can, explain how users will be able to use this and possibly write out a
version of the docs.
-->
