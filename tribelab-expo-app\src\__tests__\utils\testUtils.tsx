import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { configureStore, Store } from '@reduxjs/toolkit';
import Toast from 'react-native-toast-message';

// Import your actual reducers
import authSlice from '../../store/slices/authSlice';
import communitySlice from '../../store/slices/communitySlice';
import courseSlice from '../../store/slices/courseSlice';
import chatSlice from '../../store/slices/chatSlice';
import notificationSlice from '../../store/slices/notificationSlice';
import paymentSlice from '../../store/slices/paymentSlice';
import searchSlice from '../../store/slices/searchSlice';
import adminSlice from '../../store/slices/adminSlice';

// Mock initial states
export const mockInitialState = {
  auth: {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  },
  community: {
    communities: [],
    currentCommunity: null,
    posts: [],
    isLoading: false,
    error: null,
    pagination: {
      communities: { page: 1, hasMore: true },
      posts: { page: 1, hasMore: true },
    },
  },
  course: {
    courses: [],
    currentCourse: null,
    lessons: [],
    progress: {},
    isLoading: false,
    error: null,
    pagination: {
      courses: { page: 1, hasMore: true },
      lessons: { page: 1, hasMore: true },
    },
  },
  chat: {
    chats: [],
    currentChat: null,
    messages: [],
    typingUsers: [],
    isLoading: false,
    error: null,
    pagination: {
      chats: { page: 1, hasMore: true },
      messages: { page: 1, hasMore: true },
    },
  },
  notification: {
    notifications: [],
    unreadCount: 0,
    settings: {
      push: {
        postLikes: true,
        postComments: true,
        communityUpdates: true,
        courseUpdates: true,
        paymentUpdates: true,
        systemUpdates: true,
        messages: true,
      },
      email: {
        postLikes: false,
        postComments: true,
        communityUpdates: true,
        courseUpdates: true,
        paymentUpdates: true,
        systemUpdates: true,
        messages: false,
      },
      inApp: {
        postLikes: true,
        postComments: true,
        communityUpdates: true,
        courseUpdates: true,
        paymentUpdates: true,
        systemUpdates: true,
        messages: true,
      },
      frequency: 'immediate',
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
    },
    isLoading: false,
    error: null,
  },
  payment: {
    paymentMethods: [],
    transactions: [],
    subscriptions: [],
    isLoading: false,
    error: null,
    pagination: {
      transactions: { page: 1, hasMore: true },
      subscriptions: { page: 1, hasMore: true },
    },
  },
  search: {
    query: '',
    results: [],
    filters: {
      type: 'all',
      sortBy: 'relevance',
      category: undefined,
      dateRange: undefined,
      location: undefined,
      priceRange: undefined,
    },
    recentSearches: [],
    trendingSearches: [],
    isLoading: false,
    error: null,
  },
  admin: {
    users: [],
    reports: [],
    analytics: {},
    isLoading: false,
    error: null,
    pagination: {
      users: { page: 1, hasMore: true },
      reports: { page: 1, hasMore: true },
    },
  },
};

// Create test store
export const createTestStore = (initialState = mockInitialState): Store => {
  return configureStore({
    reducer: {
      auth: authSlice,
      community: communitySlice,
      course: courseSlice,
      chat: chatSlice,
      notification: notificationSlice,
      payment: paymentSlice,
      search: searchSlice,
      admin: adminSlice,
    },
    preloadedState: initialState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode;
  store?: Store;
  initialState?: any;
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  store, 
  initialState 
}) => {
  const testStore = store || createTestStore(initialState);
  
  return (
    <Provider store={testStore}>
      <SafeAreaProvider>
        <NavigationContainer>
          {children}
          <Toast />
        </NavigationContainer>
      </SafeAreaProvider>
    </Provider>
  );
};

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  store?: Store;
  initialState?: any;
}

export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { store, initialState, ...renderOptions } = options;
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper store={store} initialState={initialState}>
      {children}
    </TestWrapper>
  );

  return {
    store: store || createTestStore(initialState),
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Mock data generators
export const mockDataGenerators = {
  user: (overrides = {}) => ({
    id: `user-${Math.random().toString(36).substr(2, 9)}`,
    name: 'Test User',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    isVerified: true,
    role: 'user',
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  community: (overrides = {}) => ({
    id: `community-${Math.random().toString(36).substr(2, 9)}`,
    name: 'Test Community',
    description: 'A test community for testing purposes',
    avatar: 'https://example.com/community.jpg',
    banner: 'https://example.com/banner.jpg',
    memberCount: Math.floor(Math.random() * 1000) + 10,
    isPrivate: false,
    category: 'Technology',
    tags: ['react', 'mobile', 'development'],
    owner: mockDataGenerators.user(),
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  course: (overrides = {}) => ({
    id: `course-${Math.random().toString(36).substr(2, 9)}`,
    title: 'Test Course',
    description: 'A comprehensive test course',
    thumbnail: 'https://example.com/course.jpg',
    price: Math.floor(Math.random() * 200) + 50,
    duration: `${Math.floor(Math.random() * 20) + 5} hours`,
    rating: Math.round((Math.random() * 2 + 3) * 10) / 10,
    studentCount: Math.floor(Math.random() * 500) + 10,
    level: 'Beginner',
    category: 'Technology',
    instructor: mockDataGenerators.user({ role: 'instructor' }),
    lessons: [],
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  post: (overrides = {}) => ({
    id: `post-${Math.random().toString(36).substr(2, 9)}`,
    content: 'This is a test post content for testing purposes.',
    author: mockDataGenerators.user(),
    communityId: `community-${Math.random().toString(36).substr(2, 9)}`,
    likes: Math.floor(Math.random() * 100),
    comments: Math.floor(Math.random() * 50),
    shares: Math.floor(Math.random() * 20),
    images: [],
    tags: ['test', 'post'],
    isLiked: false,
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  message: (overrides = {}) => ({
    id: `message-${Math.random().toString(36).substr(2, 9)}`,
    content: 'Test message content',
    sender: mockDataGenerators.user(),
    chatId: `chat-${Math.random().toString(36).substr(2, 9)}`,
    type: 'text',
    isRead: false,
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  notification: (overrides = {}) => ({
    id: `notification-${Math.random().toString(36).substr(2, 9)}`,
    type: 'message',
    title: 'Test Notification',
    message: 'This is a test notification',
    data: {},
    isRead: false,
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  paymentMethod: (overrides = {}) => ({
    id: `pm-${Math.random().toString(36).substr(2, 9)}`,
    type: 'card',
    last4: '4242',
    brand: 'visa',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: false,
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  transaction: (overrides = {}) => ({
    id: `txn-${Math.random().toString(36).substr(2, 9)}`,
    type: 'course',
    amount: Math.floor(Math.random() * 200) + 50,
    currency: 'USD',
    status: 'completed',
    description: 'Course purchase',
    createdAt: new Date().toISOString(),
    ...overrides,
  }),
};

// Test helpers
export const testHelpers = {
  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Simulate user interactions
  simulateNetworkDelay: (min = 100, max = 500) => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  },

  // Mock API responses
  mockSuccessResponse: (data: any) => ({
    ok: true,
    status: 200,
    json: () => Promise.resolve(data),
  }),

  mockErrorResponse: (message: string, status = 400) => ({
    ok: false,
    status,
    json: () => Promise.resolve({ error: message }),
  }),

  // Generate test data arrays
  generateArray: <T>(generator: () => T, count: number): T[] => {
    return Array.from({ length: count }, generator);
  },

  // Date helpers
  getDateDaysAgo: (days: number) => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString();
  },

  getDateDaysFromNow: (days: number) => {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toISOString();
  },

  // Validation helpers
  isValidEmail: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidUrl: (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // Performance testing helpers
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    await new Promise(resolve => setTimeout(resolve, 0)); // Wait for render
    const end = performance.now();
    return end - start;
  },

  // Memory testing helpers
  getMemoryUsage: () => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
      };
    }
    return null;
  },
};

// Export everything
export * from '@testing-library/react-native';
export { renderWithProviders as render };
