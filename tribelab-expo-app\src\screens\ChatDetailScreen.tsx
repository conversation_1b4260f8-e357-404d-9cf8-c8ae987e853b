import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { 
  fetchMessages, 
  sendMessage, 
  markMessagesAsRead,
  addMessage,
  setTypingUsers,
  addTypingUser,
  removeTypingUser 
} from '../store/slices/chatSlice';
import webSocketService from '../services/websocket';
import Toast from 'react-native-toast-message';

interface ChatDetailScreenParams {
  chatId: string;
}

const ChatDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  const { chatId } = route.params as ChatDetailScreenParams;
  
  const { currentChat, messages, isLoading, typingUsers } = useAppSelector(state => state.chat);
  const { user } = useAppSelector(state => state.auth);
  
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadMessages();
    joinChatRoom();
    markAsRead();

    return () => {
      leaveChatRoom();
      stopTyping();
    };
  }, [chatId]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const loadMessages = async () => {
    try {
      await dispatch(fetchMessages({ chatId, page: 1 })).unwrap();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load messages',
      });
    }
  };

  const joinChatRoom = () => {
    webSocketService.joinChatRoom(chatId);
  };

  const leaveChatRoom = () => {
    webSocketService.leaveChatRoom(chatId);
  };

  const markAsRead = async () => {
    try {
      await dispatch(markMessagesAsRead(chatId)).unwrap();
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    const tempMessage = messageText.trim();
    setMessageText('');
    setSending(true);
    stopTyping();

    try {
      await dispatch(sendMessage({
        chatId,
        content: tempMessage,
        type: 'text',
      })).unwrap();

      // Send via WebSocket for real-time delivery
      webSocketService.sendMessage(chatId, tempMessage, 'text');
      
      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Send Failed',
        text2: error.message || 'Failed to send message',
      });
      // Restore message text on failure
      setMessageText(tempMessage);
    } finally {
      setSending(false);
    }
  };

  const handleTyping = (text: string) => {
    setMessageText(text);
    
    if (text.length > 0 && !isTyping) {
      setIsTyping(true);
      webSocketService.startTyping(chatId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 2000);
  };

  const stopTyping = () => {
    if (isTyping) {
      setIsTyping(false);
      webSocketService.stopTyping(chatId);
    }
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const isMyMessage = (message: any) => {
    return message.sender.id === user?.id;
  };

  const getOtherParticipant = () => {
    if (currentChat?.type === 'direct') {
      return currentChat.participants.find(p => p.id !== user?.id);
    }
    return null;
  };

  const getChatTitle = () => {
    if (currentChat?.type === 'group' || currentChat?.type === 'community') {
      return currentChat.name;
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.name || 'Unknown User';
  };

  const renderMessage = ({ item: message, index }: { item: any; index: number }) => {
    const isMe = isMyMessage(message);
    const showAvatar = !isMe && (index === 0 || messages[index - 1]?.sender.id !== message.sender.id);
    const showTime = index === messages.length - 1 || 
                    messages[index + 1]?.sender.id !== message.sender.id ||
                    new Date(messages[index + 1]?.createdAt).getTime() - new Date(message.createdAt).getTime() > 300000; // 5 minutes

    return (
      <View style={[styles.messageContainer, isMe && styles.myMessageContainer]}>
        {showAvatar && !isMe && (
          <Image 
            source={{ uri: message.sender.avatar || 'https://via.placeholder.com/30' }}
            style={styles.messageAvatar}
          />
        )}
        
        <View style={[styles.messageBubble, isMe ? styles.myMessageBubble : styles.otherMessageBubble]}>
          {!isMe && currentChat?.type !== 'direct' && showAvatar && (
            <Text style={styles.senderName}>{message.sender.name}</Text>
          )}
          
          <Text style={[styles.messageText, isMe && styles.myMessageText]}>
            {message.content}
          </Text>
          
          {showTime && (
            <Text style={[styles.messageTime, isMe && styles.myMessageTime]}>
              {formatMessageTime(message.createdAt)}
            </Text>
          )}
        </View>
        
        {!showAvatar && !isMe && <View style={styles.avatarSpacer} />}
      </View>
    );
  };

  const renderTypingIndicator = () => {
    const currentTypingUsers = typingUsers.filter(tu => tu.chatId === chatId && tu.userId !== user?.id);
    
    if (currentTypingUsers.length === 0) return null;

    return (
      <View style={styles.typingContainer}>
        <View style={styles.typingBubble}>
          <Text style={styles.typingText}>
            {currentTypingUsers.length === 1 
              ? `${currentTypingUsers[0].userName} is typing...`
              : `${currentTypingUsers.length} people are typing...`
            }
          </Text>
          <View style={styles.typingDots}>
            <View style={[styles.typingDot, styles.typingDot1]} />
            <View style={[styles.typingDot, styles.typingDot2]} />
            <View style={[styles.typingDot, styles.typingDot3]} />
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>{getChatTitle()}</Text>
          {currentChat?.type === 'direct' && (
            <Text style={styles.headerSubtitle}>
              {getOtherParticipant()?.isOnline ? 'Online' : 'Offline'}
            </Text>
          )}
        </View>
        
        <TouchableOpacity style={styles.headerButton}>
          <Icon name="call" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <KeyboardAvoidingView 
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            style={styles.messagesList}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
            ListFooterComponent={renderTypingIndicator}
          />
        )}

        {/* Input */}
        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.attachButton}>
            <Icon name="add" size={24} color="#007AFF" />
          </TouchableOpacity>
          
          <TextInput
            style={styles.textInput}
            value={messageText}
            onChangeText={handleTyping}
            placeholder="Type a message..."
            placeholderTextColor="#999"
            multiline
            maxLength={1000}
          />
          
          <TouchableOpacity
            style={[styles.sendButton, (!messageText.trim() || sending) && styles.sendButtonDisabled]}
            onPress={handleSendMessage}
            disabled={!messageText.trim() || sending}
          >
            {sending ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Icon name="send" size={20} color="#fff" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#007AFF',
    marginTop: 2,
  },
  headerButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  myMessageContainer: {
    justifyContent: 'flex-end',
  },
  messageAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
    marginTop: 4,
  },
  avatarSpacer: {
    width: 38,
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
  },
  myMessageBubble: {
    backgroundColor: '#007AFF',
    borderBottomRightRadius: 6,
  },
  otherMessageBubble: {
    backgroundColor: '#fff',
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: '#eee',
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 20,
  },
  myMessageText: {
    color: '#fff',
  },
  messageTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  myMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  typingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: '#eee',
    maxWidth: '75%',
  },
  typingText: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  typingDots: {
    flexDirection: 'row',
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#999',
    marginHorizontal: 1,
  },
  typingDot1: {
    // Animation would be added here
  },
  typingDot2: {
    // Animation would be added here
  },
  typingDot3: {
    // Animation would be added here
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  attachButton: {
    padding: 8,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 8,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    borderRadius: 20,
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
    minHeight: 40,
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
});

export default ChatDetailScreen;
