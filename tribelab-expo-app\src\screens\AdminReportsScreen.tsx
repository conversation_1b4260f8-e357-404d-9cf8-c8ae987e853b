import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
  FlatList,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchAdminReports, updateReportStatus } from '../store/slices/adminSlice';
import Toast from 'react-native-toast-message';

interface Report {
  id: string;
  type: 'user' | 'community' | 'post' | 'comment' | 'spam' | 'harassment' | 'inappropriate_content';
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  reporter: {
    id: string;
    name: string;
    email: string;
  };
  reported: {
    id: string;
    type: 'user' | 'community' | 'post' | 'comment';
    title: string;
    content?: string;
  };
  reason: string;
  description: string;
  evidence?: string[];
  createdAt: string;
  updatedAt: string;
  assignedTo?: {
    id: string;
    name: string;
  };
  resolution?: {
    action: string;
    reason: string;
    resolvedBy: string;
    resolvedAt: string;
  };
}

const AdminReportsScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { reports, isLoading, pagination } = useAppSelector(state => state.admin);
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'investigating' | 'resolved' | 'dismissed'>('all');
  const [filterType, setFilterType] = useState<'all' | 'user' | 'community' | 'post' | 'comment'>('all');
  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');

  useEffect(() => {
    loadReports();
  }, [filterStatus, filterType, filterPriority]);

  const loadReports = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      }
      
      const filters = {
        status: filterStatus !== 'all' ? filterStatus : undefined,
        type: filterType !== 'all' ? filterType : undefined,
        priority: filterPriority !== 'all' ? filterPriority : undefined,
      };
      
      await dispatch(fetchAdminReports({ page: 1, filters })).unwrap();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load reports',
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleReportPress = (report: Report) => {
    setSelectedReport(report);
    setShowDetailModal(true);
  };

  const handleStatusUpdate = async (reportId: string, newStatus: Report['status']) => {
    try {
      await dispatch(updateReportStatus({ reportId, status: newStatus })).unwrap();
      
      Toast.show({
        type: 'success',
        text1: 'Status Updated',
        text2: `Report status changed to ${newStatus}`,
      });
      
      setShowDetailModal(false);
      loadReports(true);
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Update Failed',
        text2: error.message || 'Failed to update report status',
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#ffc107';
      case 'investigating':
        return '#007AFF';
      case 'resolved':
        return '#28a745';
      case 'dismissed':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return '#dc3545';
      case 'high':
        return '#fd7e14';
      case 'medium':
        return '#ffc107';
      case 'low':
        return '#28a745';
      default:
        return '#6c757d';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'user':
        return 'person';
      case 'community':
        return 'people';
      case 'post':
        return 'document-text';
      case 'comment':
        return 'chatbubble';
      default:
        return 'flag';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderReport = ({ item: report }: { item: Report }) => (
    <TouchableOpacity
      style={styles.reportCard}
      onPress={() => handleReportPress(report)}
    >
      <View style={styles.reportHeader}>
        <View style={styles.reportInfo}>
          <Icon 
            name={getTypeIcon(report.type)} 
            size={20} 
            color="#007AFF" 
            style={styles.typeIcon}
          />
          <View style={styles.reportDetails}>
            <Text style={styles.reportTitle} numberOfLines={1}>
              {report.reported.title}
            </Text>
            <Text style={styles.reportReason} numberOfLines={1}>
              {report.reason}
            </Text>
          </View>
        </View>
        
        <View style={styles.reportMeta}>
          <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(report.priority) }]}>
            <Text style={styles.priorityText}>{report.priority.toUpperCase()}</Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(report.status) }]}>
            <Text style={styles.statusText}>{report.status.toUpperCase()}</Text>
          </View>
        </View>
      </View>

      <View style={styles.reportFooter}>
        <Text style={styles.reportDate}>
          Reported by {report.reporter.name} • {formatDate(report.createdAt)}
        </Text>
        {report.assignedTo && (
          <Text style={styles.assignedTo}>
            Assigned to {report.assignedTo.name}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {/* Status Filter */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Status:</Text>
          {['all', 'pending', 'investigating', 'resolved', 'dismissed'].map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterTab,
                filterStatus === status && styles.filterTabActive
              ]}
              onPress={() => setFilterStatus(status as any)}
            >
              <Text style={[
                styles.filterTabText,
                filterStatus === status && styles.filterTabTextActive
              ]}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Priority Filter */}
        <View style={styles.filterGroup}>
          <Text style={styles.filterLabel}>Priority:</Text>
          {['all', 'critical', 'high', 'medium', 'low'].map((priority) => (
            <TouchableOpacity
              key={priority}
              style={[
                styles.filterTab,
                filterPriority === priority && styles.filterTabActive
              ]}
              onPress={() => setFilterPriority(priority as any)}
            >
              <Text style={[
                styles.filterTabText,
                filterPriority === priority && styles.filterTabTextActive
              ]}>
                {priority.charAt(0).toUpperCase() + priority.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reports & Moderation</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={() => loadReports(true)}>
          <Icon name="refresh" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      {renderFilterTabs()}

      {/* Reports List */}
      {isLoading && reports.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading reports...</Text>
        </View>
      ) : (
        <FlatList
          data={reports}
          renderItem={renderReport}
          keyExtractor={(item) => item.id}
          style={styles.reportsList}
          contentContainerStyle={styles.reportsContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={() => loadReports(true)} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Icon name="shield-checkmark-outline" size={64} color="#ccc" />
              <Text style={styles.emptyTitle}>No Reports Found</Text>
              <Text style={styles.emptyText}>
                All reports have been handled or no reports match your filters
              </Text>
            </View>
          }
        />
      )}

      {/* Report Detail Modal */}
      <Modal
        visible={showDetailModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowDetailModal(false)}
      >
        {selectedReport && (
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowDetailModal(false)}
              >
                <Icon name="close" size={24} color="#333" />
              </TouchableOpacity>
              <Text style={styles.modalTitle}>Report Details</Text>
              <View style={styles.placeholder} />
            </View>

            <ScrollView style={styles.modalContent}>
              <View style={styles.detailSection}>
                <Text style={styles.detailTitle}>Reported Content</Text>
                <Text style={styles.detailText}>{selectedReport.reported.title}</Text>
                {selectedReport.reported.content && (
                  <Text style={styles.detailContent}>{selectedReport.reported.content}</Text>
                )}
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailTitle}>Report Reason</Text>
                <Text style={styles.detailText}>{selectedReport.reason}</Text>
                <Text style={styles.detailDescription}>{selectedReport.description}</Text>
              </View>

              <View style={styles.detailSection}>
                <Text style={styles.detailTitle}>Reporter Information</Text>
                <Text style={styles.detailText}>
                  {selectedReport.reporter.name} ({selectedReport.reporter.email})
                </Text>
              </View>

              {selectedReport.resolution && (
                <View style={styles.detailSection}>
                  <Text style={styles.detailTitle}>Resolution</Text>
                  <Text style={styles.detailText}>{selectedReport.resolution.action}</Text>
                  <Text style={styles.detailDescription}>{selectedReport.resolution.reason}</Text>
                  <Text style={styles.detailMeta}>
                    Resolved by {selectedReport.resolution.resolvedBy} on {formatDate(selectedReport.resolution.resolvedAt)}
                  </Text>
                </View>
              )}

              {/* Action Buttons */}
              {selectedReport.status === 'pending' && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.investigateButton]}
                    onPress={() => handleStatusUpdate(selectedReport.id, 'investigating')}
                  >
                    <Text style={styles.actionButtonText}>Start Investigation</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.actionButton, styles.dismissButton]}
                    onPress={() => handleStatusUpdate(selectedReport.id, 'dismissed')}
                  >
                    <Text style={styles.actionButtonText}>Dismiss</Text>
                  </TouchableOpacity>
                </View>
              )}

              {selectedReport.status === 'investigating' && (
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.resolveButton]}
                    onPress={() => handleStatusUpdate(selectedReport.id, 'resolved')}
                  >
                    <Text style={styles.actionButtonText}>Mark Resolved</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.actionButton, styles.dismissButton]}
                    onPress={() => handleStatusUpdate(selectedReport.id, 'dismissed')}
                  >
                    <Text style={styles.actionButtonText}>Dismiss</Text>
                  </TouchableOpacity>
                </View>
              )}
            </ScrollView>
          </SafeAreaView>
        )}
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshButton: {
    padding: 5,
  },
  filterContainer: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  filterGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginRight: 20,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
  },
  filterTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  filterTabActive: {
    backgroundColor: '#007AFF',
  },
  filterTabText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  filterTabTextActive: {
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  reportsList: {
    flex: 1,
  },
  reportsContent: {
    padding: 16,
  },
  reportCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  reportInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIcon: {
    marginRight: 12,
  },
  reportDetails: {
    flex: 1,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  reportReason: {
    fontSize: 14,
    color: '#666',
  },
  reportMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: 'bold',
  },
  reportFooter: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 8,
  },
  reportDate: {
    fontSize: 12,
    color: '#999',
  },
  assignedTo: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  detailSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  detailTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  detailContent: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
  },
  detailDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  detailMeta: {
    fontSize: 12,
    color: '#999',
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  investigateButton: {
    backgroundColor: '#007AFF',
  },
  resolveButton: {
    backgroundColor: '#28a745',
  },
  dismissButton: {
    backgroundColor: '#6c757d',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AdminReportsScreen;
