import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
} from 'react-native';

const TouchTestScreen = () => {
  const [text, setText] = useState('');
  const [buttonPressed, setButtonPressed] = useState(false);

  const handleButtonPress = () => {
    console.log('TEST: Button was pressed!');
    Alert.alert('Success!', 'Touch is working!');
    setButtonPressed(true);
  };

  const handleTextChange = (newText: string) => {
    console.log('TEST: Text input changed:', newText);
    setText(newText);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Touch Test Screen</Text>
      
      <TouchableOpacity 
        style={styles.button} 
        onPress={handleButtonPress}
        activeOpacity={0.7}
      >
        <Text style={styles.buttonText}>
          {buttonPressed ? 'Button Works!' : 'Tap Me'}
        </Text>
      </TouchableOpacity>

      <TextInput
        style={styles.input}
        placeholder="Type here to test input"
        value={text}
        onChangeText={handleTextChange}
        onFocus={() => console.log('TEST: Input focused')}
        onBlur={() => console.log('TEST: Input blurred')}
      />

      <Text style={styles.result}>
        Text: {text || 'No text entered'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 30,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  input: {
    width: '100%',
    height: 50,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    marginBottom: 20,
    backgroundColor: '#fff',
  },
  result: {
    fontSize: 16,
    color: '#666',
  },
});

export default TouchTestScreen;
