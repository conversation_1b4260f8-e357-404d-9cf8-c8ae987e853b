import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { 
  fetchSubscriptions, 
  fetchPlans, 
  cancelSubscription,
  setCurrentSubscription 
} from '../store/slices/paymentSlice';
import paymentService from '../services/payment';
import Toast from 'react-native-toast-message';

const SubscriptionScreen = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const { subscriptions, plans, currentSubscription, isLoading } = useAppSelector(state => state.payment);
  
  const [activeTab, setActiveTab] = useState<'current' | 'plans'>('current');
  const [cancelling, setCancelling] = useState<string | null>(null);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      await Promise.all([
        dispatch(fetchSubscriptions()).unwrap(),
        dispatch(fetchPlans({})).unwrap(),
      ]);
    } catch (error) {
      console.error('Failed to load subscription data:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load subscription data',
      });
    }
  };

  const handleCancelSubscription = (subscriptionId: string) => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel this subscription? You will lose access at the end of your current billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel Subscription',
          style: 'destructive',
          onPress: () => confirmCancelSubscription(subscriptionId),
        },
      ]
    );
  };

  const confirmCancelSubscription = async (subscriptionId: string) => {
    try {
      setCancelling(subscriptionId);
      await dispatch(cancelSubscription({ subscriptionId, immediate: false })).unwrap();
      
      Toast.show({
        type: 'success',
        text1: 'Subscription Cancelled',
        text2: 'Your subscription will end at the current billing period',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Cancellation Failed',
        text2: error.message || 'Failed to cancel subscription',
      });
    } finally {
      setCancelling(null);
    }
  };

  const handleSubscribeToPlan = (planId: string) => {
    const plan = plans.find(p => p.id === planId);
    if (plan) {
      navigation.navigate('Payment', {
        type: 'subscription',
        itemId: planId,
        amount: plan.amount,
        currency: plan.currency,
        title: plan.name,
        description: plan.description,
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#28a745';
      case 'trial':
        return '#007AFF';
      case 'cancelled':
        return '#dc3545';
      case 'expired':
        return '#6c757d';
      default:
        return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'trial':
        return 'Trial';
      case 'cancelled':
        return 'Cancelled';
      case 'expired':
        return 'Expired';
      default:
        return status;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Subscriptions</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'current' && styles.activeTab]}
          onPress={() => setActiveTab('current')}
        >
          <Text style={[styles.tabText, activeTab === 'current' && styles.activeTabText]}>
            Current
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'plans' && styles.activeTab]}
          onPress={() => setActiveTab('plans')}
        >
          <Text style={[styles.tabText, activeTab === 'plans' && styles.activeTabText]}>
            Available Plans
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {activeTab === 'current' ? (
          // Current Subscriptions
          <View style={styles.section}>
            {isLoading ? (
              <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : subscriptions.length > 0 ? (
              subscriptions.map((subscription) => (
                <View key={subscription.id} style={styles.subscriptionCard}>
                  <View style={styles.subscriptionHeader}>
                    <Text style={styles.subscriptionName}>{subscription.planName}</Text>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(subscription.status) }]}>
                      <Text style={styles.statusText}>{getStatusText(subscription.status)}</Text>
                    </View>
                  </View>

                  <Text style={styles.subscriptionPrice}>
                    {paymentService.formatCurrency(subscription.amount, subscription.currency)}
                    <Text style={styles.interval}>/{subscription.interval}</Text>
                  </Text>

                  {subscription.communityName && (
                    <Text style={styles.communityName}>
                      Community: {subscription.communityName}
                    </Text>
                  )}

                  <View style={styles.subscriptionDetails}>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Current Period:</Text>
                      <Text style={styles.detailValue}>
                        {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                      </Text>
                    </View>

                    {subscription.trialEnd && (
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Trial Ends:</Text>
                        <Text style={styles.detailValue}>{formatDate(subscription.trialEnd)}</Text>
                      </View>
                    )}

                    {subscription.cancelAtPeriodEnd && (
                      <View style={styles.warningBox}>
                        <Icon name="warning" size={16} color="#ff9500" />
                        <Text style={styles.warningText}>
                          This subscription will end on {formatDate(subscription.currentPeriodEnd)}
                        </Text>
                      </View>
                    )}
                  </View>

                  {subscription.status === 'active' && !subscription.cancelAtPeriodEnd && (
                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={() => handleCancelSubscription(subscription.id)}
                      disabled={cancelling === subscription.id}
                    >
                      {cancelling === subscription.id ? (
                        <ActivityIndicator size="small" color="#dc3545" />
                      ) : (
                        <Text style={styles.cancelButtonText}>Cancel Subscription</Text>
                      )}
                    </TouchableOpacity>
                  )}
                </View>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Icon name="card-outline" size={64} color="#ccc" />
                <Text style={styles.emptyStateTitle}>No Active Subscriptions</Text>
                <Text style={styles.emptyStateText}>
                  Browse available plans to get started
                </Text>
                <TouchableOpacity
                  style={styles.browsePlansButton}
                  onPress={() => setActiveTab('plans')}
                >
                  <Text style={styles.browsePlansButtonText}>Browse Plans</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ) : (
          // Available Plans
          <View style={styles.section}>
            {isLoading ? (
              <ActivityIndicator size="large" color="#007AFF" style={styles.loader} />
            ) : (
              plans.map((plan) => (
                <View key={plan.id} style={[styles.planCard, plan.isPopular && styles.popularPlan]}>
                  {plan.isPopular && (
                    <View style={styles.popularBadge}>
                      <Text style={styles.popularBadgeText}>Most Popular</Text>
                    </View>
                  )}

                  <Text style={styles.planName}>{plan.name}</Text>
                  <Text style={styles.planDescription}>{plan.description}</Text>
                  
                  <Text style={styles.planPrice}>
                    {paymentService.formatCurrency(plan.amount, plan.currency)}
                    <Text style={styles.planInterval}>/{plan.interval}</Text>
                  </Text>

                  {plan.trialDays && (
                    <Text style={styles.trialInfo}>
                      {plan.trialDays} days free trial
                    </Text>
                  )}

                  <View style={styles.featuresList}>
                    {plan.features.map((feature, index) => (
                      <View key={index} style={styles.featureItem}>
                        <Icon name="checkmark" size={16} color="#28a745" />
                        <Text style={styles.featureText}>{feature}</Text>
                      </View>
                    ))}
                  </View>

                  <TouchableOpacity
                    style={[styles.subscribeButton, plan.isPopular && styles.popularSubscribeButton]}
                    onPress={() => handleSubscribeToPlan(plan.id)}
                  >
                    <Text style={[styles.subscribeButtonText, plan.isPopular && styles.popularSubscribeButtonText]}>
                      {plan.trialDays ? 'Start Free Trial' : 'Subscribe'}
                    </Text>
                  </TouchableOpacity>
                </View>
              ))
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  loader: {
    marginVertical: 40,
  },
  subscriptionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#eee',
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  subscriptionName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  subscriptionPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  interval: {
    fontSize: 16,
    color: '#666',
  },
  communityName: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  subscriptionDetails: {
    marginBottom: 15,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff3cd',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  warningText: {
    fontSize: 14,
    color: '#856404',
    marginLeft: 8,
    flex: 1,
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: '#dc3545',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#dc3545',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  browsePlansButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  browsePlansButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  planCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#eee',
    position: 'relative',
  },
  popularPlan: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    left: 20,
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    marginTop: 10,
  },
  planDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  planPrice: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  planInterval: {
    fontSize: 16,
    color: '#666',
  },
  trialInfo: {
    fontSize: 14,
    color: '#28a745',
    fontWeight: '500',
    marginBottom: 20,
  },
  featuresList: {
    marginBottom: 25,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 10,
    flex: 1,
  },
  subscribeButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
  },
  popularSubscribeButton: {
    backgroundColor: '#007AFF',
  },
  subscribeButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  popularSubscribeButtonText: {
    color: '#fff',
  },
});

export default SubscriptionScreen;
