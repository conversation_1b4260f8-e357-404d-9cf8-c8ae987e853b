import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { addNotification, updateUnreadCount } from '../store/slices/notificationSlice';
import notificationService from '../services/notifications';
import RealtimeNotificationBanner from '../components/RealtimeNotificationBanner';
import webSocketService from '../services/websocket';

interface NotificationContextType {
  showBanner: (notification: any) => void;
  hideBanner: () => void;
  isConnected: boolean;
  unreadCount: number;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { unreadCount } = useAppSelector(state => state.notification);
  
  const [currentBanner, setCurrentBanner] = useState<any>(null);
  const [bannerQueue, setBannerQueue] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);

  useEffect(() => {
    if (user) {
      initializeNotifications();
      setupWebSocketListeners();
      setupAppStateListener();
    }

    return () => {
      cleanup();
    };
  }, [user]);

  useEffect(() => {
    // Update badge count when unread count changes
    notificationService.setBadgeCount(unreadCount);
  }, [unreadCount]);

  useEffect(() => {
    // Process banner queue
    if (!currentBanner && bannerQueue.length > 0) {
      const nextBanner = bannerQueue[0];
      setBannerQueue(prev => prev.slice(1));
      setCurrentBanner(nextBanner);
    }
  }, [currentBanner, bannerQueue]);

  const initializeNotifications = async () => {
    try {
      await notificationService.initialize();
      await notificationService.setNotificationCategories();
      console.log('Notification provider initialized');
    } catch (error) {
      console.error('Failed to initialize notification provider:', error);
    }
  };

  const setupWebSocketListeners = () => {
    if (!user) return;

    try {
      // Connect to WebSocket
      webSocketService.connect(user.id);
      
      // Listen for connection status
      webSocketService.on('connect', () => {
        setIsConnected(true);
        console.log('WebSocket connected for notifications');
      });

      webSocketService.on('disconnect', () => {
        setIsConnected(false);
        console.log('WebSocket disconnected');
      });

      // Listen for real-time notifications
      webSocketService.on('notification', (data: any) => {
        handleRealtimeNotification(data);
      });

      // Listen for unread count updates
      webSocketService.on('unread_count', (data: { count: number }) => {
        dispatch(updateUnreadCount(data.count));
      });

      // Listen for specific notification types
      webSocketService.on('message_received', (data: any) => {
        handleMessageNotification(data);
      });

      webSocketService.on('community_update', (data: any) => {
        handleCommunityNotification(data);
      });

      webSocketService.on('course_update', (data: any) => {
        handleCourseNotification(data);
      });

      webSocketService.on('payment_update', (data: any) => {
        handlePaymentNotification(data);
      });

    } catch (error) {
      console.error('Failed to setup WebSocket listeners:', error);
    }
  };

  const setupAppStateListener = () => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // App came to foreground - clear badge and refresh notifications
        notificationService.clearBadge();
        // Optionally refresh notification count from server
      }
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  };

  const cleanup = () => {
    webSocketService.disconnect();
    setIsConnected(false);
  };

  const handleRealtimeNotification = (data: any) => {
    try {
      const notification = {
        id: data.id || Date.now().toString(),
        type: data.type || 'system',
        title: data.title || 'New Notification',
        message: data.message || '',
        data: data.data || {},
        isRead: false,
        createdAt: new Date().toISOString(),
        user: data.user || { id: 'system', name: 'System' },
        priority: data.priority || 'medium',
      };

      // Add to Redux store
      dispatch(addNotification(notification));

      // Show banner if app is in foreground
      if (appState === 'active') {
        showBanner(notification);
      } else {
        // Send push notification if app is in background
        notificationService.scheduleLocalNotification(
          notification.title,
          notification.message,
          notification.data
        );
      }
    } catch (error) {
      console.error('Error handling realtime notification:', error);
    }
  };

  const handleMessageNotification = (data: any) => {
    const notification = {
      id: `message_${data.messageId}`,
      type: 'message',
      title: `New message from ${data.senderName}`,
      message: data.message,
      data: {
        chatId: data.chatId,
        senderId: data.senderId,
        messageId: data.messageId,
      },
      user: {
        id: data.senderId,
        name: data.senderName,
        avatar: data.senderAvatar,
      },
      priority: 'high',
    };

    handleRealtimeNotification(notification);
  };

  const handleCommunityNotification = (data: any) => {
    const notification = {
      id: `community_${data.communityId}_${Date.now()}`,
      type: 'community',
      title: data.title || 'Community Update',
      message: data.message,
      data: {
        communityId: data.communityId,
        postId: data.postId,
        type: data.updateType,
      },
      user: data.user,
      priority: 'medium',
    };

    handleRealtimeNotification(notification);
  };

  const handleCourseNotification = (data: any) => {
    const notification = {
      id: `course_${data.courseId}_${Date.now()}`,
      type: 'course',
      title: data.title || 'Course Update',
      message: data.message,
      data: {
        courseId: data.courseId,
        lessonId: data.lessonId,
        type: data.updateType,
      },
      user: data.instructor,
      priority: 'medium',
    };

    handleRealtimeNotification(notification);
  };

  const handlePaymentNotification = (data: any) => {
    const notification = {
      id: `payment_${data.transactionId}`,
      type: 'payment',
      title: data.title || 'Payment Update',
      message: data.message,
      data: {
        transactionId: data.transactionId,
        amount: data.amount,
        status: data.status,
      },
      user: { id: 'system', name: 'TribeLab' },
      priority: 'high',
    };

    handleRealtimeNotification(notification);
  };

  const showBanner = (notification: any) => {
    if (currentBanner) {
      // Add to queue if banner is already showing
      setBannerQueue(prev => [...prev, notification]);
    } else {
      setCurrentBanner(notification);
    }
  };

  const hideBanner = () => {
    setCurrentBanner(null);
  };

  const contextValue: NotificationContextType = {
    showBanner,
    hideBanner,
    isConnected,
    unreadCount,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Notification Banner */}
      {currentBanner && (
        <RealtimeNotificationBanner
          notification={currentBanner}
          onDismiss={hideBanner}
          duration={4000}
        />
      )}
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
