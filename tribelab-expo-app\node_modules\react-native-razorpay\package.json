{"name": "react-native-razorpay", "version": "2.3.0", "description": "React Native wrapper for Razorpay", "main": "RazorpayCheckout.js", "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/razorpay/react-native-razorpay.git"}, "peerDependencies": {"react": ">=16.8.0", "react-native": ">=0.66.0"}, "keywords": ["react-native", "razorpay", "payments"], "author": "Razorpay Developers <<EMAIL>> (https://razorpay.com/)", "license": "MIT", "homepage": "https://github.com/razorpay/react-native-razorpay#readme"}